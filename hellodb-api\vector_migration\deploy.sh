#!/bin/bash

# HelloDB向量数据库迁移 - 生产环境部署脚本
# 版本: 1.0.0
# 日期: 2025-07-21

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_banner() {
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                HelloDB 向量数据库迁移部署脚本                    ║"
    echo "║                                                              ║"
    echo "║  版本: 1.0.0                                                 ║"
    echo "║  日期: 2025-07-21                                            ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo
}

# 检查Python环境
check_python() {
    print_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi
    
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    print_success "Python版本: $python_version"
}

# 检查必要的Python包
check_dependencies() {
    print_info "检查Python依赖包..."
    
    required_packages=("chromadb" "sqlalchemy" "psycopg2")
    missing_packages=()
    
    for package in "${required_packages[@]}"; do
        if ! python3 -c "import $package" &> /dev/null; then
            missing_packages+=("$package")
        fi
    done
    
    if [ ${#missing_packages[@]} -ne 0 ]; then
        print_warning "缺少以下依赖包: ${missing_packages[*]}"
        print_info "尝试安装缺少的包..."
        
        for package in "${missing_packages[@]}"; do
            if [ "$package" = "psycopg2" ]; then
                pip3 install psycopg2-binary
            else
                pip3 install "$package"
            fi
        done
    fi
    
    print_success "Python依赖检查完成"
}

# 检查环境变量
check_environment() {
    print_info "检查环境变量..."
    
    required_vars=("DASHSCOPE_API_KEY")
    missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        print_warning "缺少以下环境变量: ${missing_vars[*]}"
        print_info "请设置环境变量后重新运行"
        
        echo "示例:"
        echo "export DASHSCOPE_API_KEY=\"your-api-key\""
        echo "export DB_HOST=\"your-db-host\""
        echo "export DB_PASSWORD=\"your-password\""
        echo
        
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    print_success "环境变量检查完成"
}

# 创建必要的目录
create_directories() {
    print_info "创建必要的目录..."
    
    directories=("logs" "temp" "temp/reports")
    
    for dir in "${directories[@]}"; do
        mkdir -p "vector_migration/$dir"
        print_success "创建目录: vector_migration/$dir"
    done
}

# 检查系统状态
check_system_status() {
    print_info "检查系统状态..."
    
    python3 vector_migration/migrate.py status
    
    if [ $? -eq 0 ]; then
        print_success "系统状态检查通过"
    else
        print_error "系统状态检查失败"
        exit 1
    fi
}

# 执行迁移
run_migration() {
    print_info "开始执行迁移..."
    
    # 询问迁移模式
    echo "请选择迁移模式:"
    echo "1) 全量迁移所有应用"
    echo "2) 迁移指定应用"
    echo "3) 仅验证现有迁移"
    echo "4) 退出"
    
    read -p "请输入选择 (1-4): " choice
    
    case $choice in
        1)
            print_info "执行全量迁移..."
            python3 vector_migration/migrate.py migrate-all --verify
            ;;
        2)
            read -p "请输入应用别名（逗号分隔）: " apps
            print_info "迁移指定应用: $apps"
            python3 vector_migration/migrate.py migrate-all --apps "$apps" --verify
            ;;
        3)
            print_info "验证现有迁移..."
            python3 vector_migration/migrate.py verify
            ;;
        4)
            print_info "退出部署脚本"
            exit 0
            ;;
        *)
            print_error "无效选择"
            exit 1
            ;;
    esac
    
    if [ $? -eq 0 ]; then
        print_success "迁移执行完成"
    else
        print_error "迁移执行失败"
        exit 1
    fi
}

# 显示结果
show_results() {
    print_info "迁移结果文件:"
    
    # 显示最新的日志文件
    latest_log=$(ls -t vector_migration/logs/migration_*.log 2>/dev/null | head -1)
    if [ -n "$latest_log" ]; then
        print_success "日志文件: $latest_log"
    fi
    
    # 显示最新的报告文件
    latest_report=$(ls -t vector_migration/temp/reports/migration_report_*.json 2>/dev/null | head -1)
    if [ -n "$latest_report" ]; then
        print_success "迁移报告: $latest_report"
    fi
    
    latest_verification=$(ls -t vector_migration/temp/reports/verification_report_*.json 2>/dev/null | head -1)
    if [ -n "$latest_verification" ]; then
        print_success "验证报告: $latest_verification"
    fi
}

# 清理临时文件
cleanup() {
    print_info "清理临时文件..."
    
    # 保留最近5个日志文件
    ls -t vector_migration/logs/migration_*.log 2>/dev/null | tail -n +6 | xargs rm -f
    
    # 保留最近5个报告文件
    ls -t vector_migration/temp/reports/*_report_*.json 2>/dev/null | tail -n +11 | xargs rm -f
    
    print_success "清理完成"
}

# 主函数
main() {
    print_banner
    
    # 检查当前目录
    if [ ! -f "vector_migration/migrate.py" ]; then
        print_error "请在hellodb-api目录下运行此脚本"
        exit 1
    fi
    
    # 执行检查步骤
    check_python
    check_dependencies
    check_environment
    create_directories
    check_system_status
    
    # 执行迁移
    run_migration
    
    # 显示结果
    show_results
    
    # 清理临时文件
    cleanup
    
    print_success "部署脚本执行完成！"
    echo
    echo "🎉 迁移任务完成！"
    echo "📋 请查看日志和报告文件了解详细信息"
    echo "🔍 建议进行用户验收测试确保系统正常运行"
}

# 捕获中断信号
trap 'print_error "脚本被中断"; exit 1' INT TERM

# 运行主函数
main "$@"
