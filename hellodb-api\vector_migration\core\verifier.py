#!/usr/bin/env python3
"""
向量数据库迁移验证工具
"""
import os
import sys
import json
import time
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

# 添加项目路径到sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from vector_migration.config.migration_config import config
from core.hellodb.vector.chromadb_vectorsotre import ChromaDB_VectorStore

class MigrationVerifier:
    """迁移验证器"""
    
    def __init__(self):
        self.config = config
        self.logger = self._setup_logger()
        self.verification_log = []
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('MigrationVerifier')
        logger.setLevel(getattr(logging, self.config.LOG_CONFIG['level']))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        formatter = logging.Formatter(self.config.LOG_CONFIG['format'])
        
        # 控制台处理器
        if self.config.LOG_CONFIG['console_enabled']:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] [{level}] {message}"
        print(log_entry)
        self.verification_log.append({
            'timestamp': timestamp,
            'level': level,
            'message': message
        })
    
    def get_new_vector_store(self, app_alias: str) -> Optional[ChromaDB_VectorStore]:
        """获取新的向量存储"""
        new_path = self.config.get_new_vector_path(app_alias)
        vector_config = {
            'path': new_path,
            'dashscope_api_key': self.config.DASHSCOPE_API_KEY
        }
        
        try:
            return ChromaDB_VectorStore(config=vector_config)
        except Exception as e:
            self.log(f"无法连接到新向量存储 {new_path}: {str(e)}", "ERROR")
            return None
    
    def verify_data_counts(self, app_alias: str) -> Optional[Dict[str, int]]:
        """验证数据数量"""
        self.log(f"验证应用 {app_alias} 的数据数量...")
        
        new_store = self.get_new_vector_store(app_alias)
        if not new_store:
            return None
        
        try:
            sql_count = new_store.sql_collection.count()
            ddl_count = new_store.ddl_collection.count()
            doc_count = new_store.documentation_collection.count()
            
            self.log(f"  SQL collection: {sql_count} 个文档")
            self.log(f"  DDL collection: {ddl_count} 个文档")
            self.log(f"  Documentation collection: {doc_count} 个文档")
            
            return {
                'sql_count': sql_count,
                'ddl_count': ddl_count,
                'documentation_count': doc_count,
                'total_count': sql_count + ddl_count + doc_count
            }
        except Exception as e:
            self.log(f"验证数据数量失败: {str(e)}", "ERROR")
            return None
    
    def test_retrieval_functionality(self, app_alias: str) -> Optional[Dict[str, bool]]:
        """测试检索功能"""
        self.log(f"测试应用 {app_alias} 的检索功能...")
        
        new_store = self.get_new_vector_store(app_alias)
        if not new_store:
            return None
        
        test_results = {
            'documentation_retrieval': False,
            'sql_retrieval': False,
            'ddl_retrieval': False
        }
        
        try:
            # 测试documentation检索
            doc_results = new_store.get_related_documentation("测试")
            if doc_results:
                self.log(f"  Documentation检索成功: 返回 {len(doc_results)} 个结果")
                test_results['documentation_retrieval'] = True
            else:
                self.log("  Documentation检索返回空结果")
            
            # 测试SQL检索
            sql_results = new_store.get_similar_question_sql("查询")
            if sql_results:
                self.log(f"  SQL检索成功: 返回 {len(sql_results)} 个结果")
                test_results['sql_retrieval'] = True
            else:
                self.log("  SQL检索返回空结果")
            
            # 测试DDL检索
            ddl_results = new_store.get_related_ddl("表")
            if ddl_results:
                self.log(f"  DDL检索成功: 返回 {len(ddl_results)} 个结果")
                test_results['ddl_retrieval'] = True
            else:
                self.log("  DDL检索返回空结果")
            
            return test_results
            
        except Exception as e:
            self.log(f"测试检索功能失败: {str(e)}", "ERROR")
            return None
    
    def test_embedding_quality(self, app_alias: str) -> Optional[Dict[str, Dict]]:
        """测试embedding质量"""
        self.log(f"测试应用 {app_alias} 的embedding质量...")
        
        new_store = self.get_new_vector_store(app_alias)
        if not new_store:
            return None
        
        try:
            test_queries = ["数据库", "查询", "表结构", "用户信息"]
            quality_results = {}
            
            for query in test_queries:
                start_time = time.time()
                results = new_store.get_related_documentation(query)
                end_time = time.time()
                
                response_time = end_time - start_time
                result_count = len(results) if results else 0
                
                quality_results[query] = {
                    'result_count': result_count,
                    'response_time': response_time,
                    'has_results': result_count > 0
                }
                
                self.log(f"  查询 '{query}': {result_count} 个结果, 响应时间: {response_time:.3f}秒")
            
            return quality_results
            
        except Exception as e:
            self.log(f"测试embedding质量失败: {str(e)}", "ERROR")
            return None
    
    def verify_single_app(self, app_alias: str) -> Dict[str, Any]:
        """验证单个应用"""
        self.log(f"开始验证应用: {app_alias}")
        
        verification_result = {
            'app_alias': app_alias,
            'data_counts': None,
            'retrieval_functionality': None,
            'embedding_quality': None,
            'overall_success': False
        }
        
        # 验证数据数量
        data_counts = self.verify_data_counts(app_alias)
        verification_result['data_counts'] = data_counts
        
        if data_counts is None:
            self.log(f"应用 {app_alias} 数据数量验证失败", "ERROR")
            return verification_result
        
        # 测试检索功能
        retrieval_results = self.test_retrieval_functionality(app_alias)
        verification_result['retrieval_functionality'] = retrieval_results
        
        # 测试embedding质量
        quality_results = self.test_embedding_quality(app_alias)
        verification_result['embedding_quality'] = quality_results
        
        # 判断整体成功
        if data_counts and retrieval_results is not None and quality_results:
            verification_result['overall_success'] = True
            self.log(f"应用 {app_alias} 验证成功", "SUCCESS")
        else:
            self.log(f"应用 {app_alias} 验证失败", "ERROR")
        
        return verification_result
    
    def verify_all_apps(self, app_list: List[str]) -> Dict[str, Any]:
        """验证所有应用"""
        self.log(f"开始验证 {len(app_list)} 个应用")
        
        verification_summary = {
            'total_apps': len(app_list),
            'success_count': 0,
            'error_count': 0,
            'results': []
        }
        
        for app_alias in app_list:
            try:
                result = self.verify_single_app(app_alias)
                verification_summary['results'].append(result)
                
                if result['overall_success']:
                    verification_summary['success_count'] += 1
                else:
                    verification_summary['error_count'] += 1
                    
            except Exception as e:
                self.log(f"验证应用 {app_alias} 时发生异常: {str(e)}", "ERROR")
                verification_summary['error_count'] += 1
        
        self.log(f"验证完成: 成功={verification_summary['success_count']}, 失败={verification_summary['error_count']}")
        
        return verification_summary
    
    def save_verification_report(self, summary: Dict[str, Any]) -> str:
        """保存验证报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = os.path.join(
            self.config.OUTPUT_DIRS['reports'],
            f'verification_report_{timestamp}.json'
        )
        
        report = {
            'verification_summary': summary,
            'verification_log': self.verification_log,
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.log(f"验证报告已保存到: {report_file}")
        return report_file
