#!/usr/bin/env python3
"""
ChromaDB向量数据库迁移工具
从旧的HellodbVanna系统迁移到新的Hellodb系统
"""
import os
import sys
import json
import time
import chromadb
from chromadb.config import Settings
from datetime import datetime
import hashlib

# 添加项目路径到sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.hellodb.vector.chromadb_vectorsotre import ChromaDB_VectorStore

class VectorMigrationTool:
    def __init__(self, dashscope_api_key=None):
        self.dashscope_api_key = dashscope_api_key or "sk-c4793b0c284e448ba1a611aa6f424062"
        self.migration_log = []
        self.start_time = None
        
    def log(self, message, level="INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] [{level}] {message}"
        print(log_entry)
        self.migration_log.append({
            'timestamp': timestamp,
            'level': level,
            'message': message
        })
    
    def get_old_vector_store(self, app_alias):
        """获取旧的向量存储"""
        old_path = f'/data/chroma/{app_alias}'
        if not os.path.exists(old_path):
            return None
        
        try:
            client = chromadb.PersistentClient(
                path=old_path,
                settings=Settings(anonymized_telemetry=False)
            )
            return client
        except Exception as e:
            self.log(f"无法连接到旧向量存储 {old_path}: {str(e)}", "ERROR")
            return None
    
    def get_new_vector_store(self, app_alias):
        """获取新的向量存储"""
        new_path = f'/data/hellodb/chroma/{app_alias}'
        config = {
            'path': new_path,
            'dashscope_api_key': self.dashscope_api_key
        }
        
        try:
            return ChromaDB_VectorStore(config=config)
        except Exception as e:
            self.log(f"无法创建新向量存储 {new_path}: {str(e)}", "ERROR")
            return None
    
    def extract_data_from_old_store(self, old_client):
        """从旧存储中提取数据"""
        extracted_data = {
            'sql': [],
            'ddl': [],
            'documentation': []
        }
        
        collections = ['sql', 'ddl', 'documentation']
        
        for collection_name in collections:
            try:
                collection = old_client.get_collection(collection_name)
                data = collection.get()
                
                if data and 'documents' in data:
                    for i, doc in enumerate(data['documents']):
                        item = {
                            'document': doc,
                            'metadata': data['metadatas'][i] if data['metadatas'] else {},
                            'id': data['ids'][i] if data['ids'] else None
                        }
                        extracted_data[collection_name].append(item)
                
                self.log(f"从 {collection_name} collection 提取了 {len(extracted_data[collection_name])} 个文档")
                
            except Exception as e:
                self.log(f"提取 {collection_name} collection 数据失败: {str(e)}", "ERROR")
        
        return extracted_data
    
    def migrate_data_to_new_store(self, new_store, extracted_data):
        """将数据迁移到新存储"""
        migration_stats = {
            'sql': 0,
            'ddl': 0,
            'documentation': 0,
            'errors': []
        }
        
        # 迁移SQL数据
        for item in extracted_data['sql']:
            try:
                doc_dict = json.loads(item['document'])
                new_store.add_question_sql(
                    question=doc_dict['question'],
                    sql=doc_dict['sql']
                )
                migration_stats['sql'] += 1
            except Exception as e:
                error_msg = f"迁移SQL数据失败: {str(e)}"
                self.log(error_msg, "ERROR")
                migration_stats['errors'].append(error_msg)
        
        # 迁移DDL数据
        for item in extracted_data['ddl']:
            try:
                new_store.add_ddl(item['document'])
                migration_stats['ddl'] += 1
            except Exception as e:
                error_msg = f"迁移DDL数据失败: {str(e)}"
                self.log(error_msg, "ERROR")
                migration_stats['errors'].append(error_msg)
        
        # 迁移Documentation数据
        for item in extracted_data['documentation']:
            try:
                new_store.add_documentation(item['document'])
                migration_stats['documentation'] += 1
            except Exception as e:
                error_msg = f"迁移Documentation数据失败: {str(e)}"
                self.log(error_msg, "ERROR")
                migration_stats['errors'].append(error_msg)
        
        return migration_stats
    
    def verify_migration(self, old_client, new_store):
        """验证迁移结果"""
        verification_result = {
            'success': True,
            'details': {}
        }
        
        collections = ['sql', 'ddl', 'documentation']
        
        for collection_name in collections:
            try:
                # 获取旧数据数量
                old_collection = old_client.get_collection(collection_name)
                old_count = old_collection.count()
                
                # 获取新数据数量
                if collection_name == 'sql':
                    new_count = new_store.sql_collection.count()
                elif collection_name == 'ddl':
                    new_count = new_store.ddl_collection.count()
                else:  # documentation
                    new_count = new_store.documentation_collection.count()
                
                verification_result['details'][collection_name] = {
                    'old_count': old_count,
                    'new_count': new_count,
                    'match': old_count == new_count
                }
                
                if old_count != new_count:
                    verification_result['success'] = False
                    self.log(f"{collection_name} 数据量不匹配: 旧={old_count}, 新={new_count}", "WARNING")
                else:
                    self.log(f"{collection_name} 数据量匹配: {old_count}")
                    
            except Exception as e:
                verification_result['success'] = False
                error_msg = f"验证 {collection_name} 失败: {str(e)}"
                self.log(error_msg, "ERROR")
                verification_result['details'][collection_name] = {'error': error_msg}
        
        return verification_result
    
    def migrate_single_app(self, app_alias, dry_run=False):
        """迁移单个应用的向量数据"""
        self.log(f"开始迁移应用: {app_alias}")
        
        # 获取旧向量存储
        old_client = self.get_old_vector_store(app_alias)
        if not old_client:
            self.log(f"应用 {app_alias} 的旧向量数据不存在，跳过迁移", "WARNING")
            return None
        
        # 提取旧数据
        self.log("提取旧数据...")
        extracted_data = self.extract_data_from_old_store(old_client)
        
        total_docs = sum(len(data) for data in extracted_data.values())
        if total_docs == 0:
            self.log(f"应用 {app_alias} 没有数据需要迁移", "WARNING")
            return None
        
        if dry_run:
            self.log(f"DRY RUN: 应用 {app_alias} 将迁移 {total_docs} 个文档")
            return {
                'app_alias': app_alias,
                'extracted_data': extracted_data,
                'total_docs': total_docs,
                'success': True,
                'dry_run': True
            }
        
        # 获取新向量存储
        new_store = self.get_new_vector_store(app_alias)
        if not new_store:
            self.log(f"无法创建应用 {app_alias} 的新向量存储", "ERROR")
            return None
        
        # 执行迁移
        self.log("开始数据迁移...")
        migration_stats = self.migrate_data_to_new_store(new_store, extracted_data)
        
        # 验证迁移结果
        self.log("验证迁移结果...")
        verification_result = self.verify_migration(old_client, new_store)
        
        result = {
            'app_alias': app_alias,
            'migration_stats': migration_stats,
            'verification_result': verification_result,
            'success': verification_result['success'] and len(migration_stats['errors']) == 0
        }
        
        if result['success']:
            self.log(f"应用 {app_alias} 迁移成功", "SUCCESS")
        else:
            self.log(f"应用 {app_alias} 迁移失败", "ERROR")
        
        return result

    def migrate_batch(self, app_list, dry_run=False):
        """批量迁移多个应用"""
        self.start_time = time.time()
        self.log(f"开始批量迁移 {len(app_list)} 个应用")

        results = []
        success_count = 0
        error_count = 0

        for i, app_alias in enumerate(app_list, 1):
            self.log(f"进度: {i}/{len(app_list)} - 迁移应用: {app_alias}")

            try:
                result = self.migrate_single_app(app_alias, dry_run)
                if result:
                    results.append(result)
                    if result['success']:
                        success_count += 1
                    else:
                        error_count += 1
                else:
                    self.log(f"应用 {app_alias} 跳过迁移")

                # 添加延迟避免API限流
                if not dry_run:
                    time.sleep(1)

            except Exception as e:
                error_count += 1
                error_msg = f"迁移应用 {app_alias} 时发生异常: {str(e)}"
                self.log(error_msg, "ERROR")
                results.append({
                    'app_alias': app_alias,
                    'success': False,
                    'error': error_msg
                })

        elapsed_time = time.time() - self.start_time

        summary = {
            'total_apps': len(app_list),
            'success_count': success_count,
            'error_count': error_count,
            'elapsed_time': elapsed_time,
            'results': results
        }

        self.log(f"批量迁移完成: 成功={success_count}, 失败={error_count}, 耗时={elapsed_time:.2f}秒")

        return summary

    def save_migration_report(self, summary, output_dir='migration_scripts/output'):
        """保存迁移报告"""
        os.makedirs(output_dir, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f'{output_dir}/migration_report_{timestamp}.json'

        report = {
            'migration_summary': summary,
            'migration_log': self.migration_log,
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        self.log(f"迁移报告已保存到: {report_file}")
        return report_file

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='ChromaDB向量数据库迁移工具')
    parser.add_argument('--app', type=str, help='迁移单个应用的别名')
    parser.add_argument('--batch', type=str, help='批量迁移，指定应用列表文件')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式，不实际迁移数据')
    parser.add_argument('--api-key', type=str, help='DashScope API密钥')

    args = parser.parse_args()

    # 创建迁移工具
    tool = VectorMigrationTool(dashscope_api_key=args.api_key)

    if args.app:
        # 单个应用迁移
        result = tool.migrate_single_app(args.app, dry_run=args.dry_run)
        if result:
            summary = {
                'total_apps': 1,
                'success_count': 1 if result['success'] else 0,
                'error_count': 0 if result['success'] else 1,
                'results': [result]
            }
            tool.save_migration_report(summary)

    elif args.batch:
        # 批量迁移
        if os.path.exists(args.batch):
            with open(args.batch, 'r', encoding='utf-8') as f:
                data = json.load(f)
                app_list = [app['alias'] for app in data.get('apps', [])]
        else:
            # 假设是逗号分隔的应用列表
            app_list = args.batch.split(',')

        summary = tool.migrate_batch(app_list, dry_run=args.dry_run)
        tool.save_migration_report(summary)

    else:
        # 扫描并迁移所有有数据的应用
        scan_file = 'migration_scripts/output/vector_data_scan_results.json'
        if os.path.exists(scan_file):
            with open(scan_file, 'r', encoding='utf-8') as f:
                scan_data = json.load(f)

            # 找出有数据的应用
            app_list = []
            for app_alias, app_info in scan_data.get('/data/chroma', {}).items():
                if isinstance(app_info.get('data'), dict):
                    total_docs = sum(count for count in app_info['data'].values()
                                   if isinstance(count, int))
                    if total_docs > 0:
                        app_list.append(app_alias)

            if app_list:
                tool.log(f"发现 {len(app_list)} 个有数据的应用需要迁移")
                summary = tool.migrate_batch(app_list, dry_run=args.dry_run)
                tool.save_migration_report(summary)
            else:
                tool.log("没有发现需要迁移的应用")
        else:
            tool.log(f"扫描结果文件不存在: {scan_file}", "ERROR")

if __name__ == "__main__":
    main()
