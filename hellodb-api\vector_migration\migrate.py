#!/usr/bin/env python3
"""
一键向量数据库迁移脚本
"""
import os
import sys
import argparse
import json
from datetime import datetime

# 添加项目路径到sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from vector_migration.core.migrator import VectorMigrator
from vector_migration.core.verifier import MigrationVerifier
from vector_migration.config.migration_config import config

def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    HelloDB 向量数据库迁移工具                    ║
║                                                              ║
║  功能: 将ChromaDB向量数据从旧系统迁移到新系统                    ║
║  版本: 1.0.0                                                 ║
║  时间: 2025-07-21                                            ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def print_config_info():
    """打印配置信息"""
    print("📋 当前配置信息:")
    print(f"  • 旧向量路径: {config.OLD_VECTOR_PATH}")
    print(f"  • 新向量路径: {config.NEW_VECTOR_PATH}")
    print(f"  • DashScope API: {'已配置' if config.DASHSCOPE_API_KEY else '未配置'}")
    print(f"  • 日志目录: {config.OUTPUT_DIRS['logs']}")
    print(f"  • 报告目录: {config.OUTPUT_DIRS['reports']}")
    print()

def migrate_all(args):
    """执行全量迁移"""
    print("🚀 开始执行全量迁移...")
    
    # 创建迁移器
    migrator = VectorMigrator()
    
    # 获取应用列表
    if args.apps:
        app_list = args.apps.split(',')
        print(f"📋 指定迁移应用: {app_list}")
    else:
        app_list = migrator.get_all_app_aliases()
        print(f"📋 自动发现应用: {len(app_list)} 个")
    
    if not app_list:
        print("❌ 没有找到需要迁移的应用")
        return False
    
    # 执行迁移
    migration_stats = migrator.migrate_all_apps(app_list)
    
    # 保存迁移报告
    report_file = migrator.save_migration_report()
    
    # 打印结果
    print("\n📊 迁移结果:")
    print(f"  • 总应用数: {migration_stats['total_apps']}")
    print(f"  • 迁移成功: {migration_stats['migrated_apps']}")
    print(f"  • 新建成功: {migration_stats['created_apps']}")
    print(f"  • 失败数量: {migration_stats['error_apps']}")
    print(f"  • 总耗时: {migration_stats.get('elapsed_time', 0):.2f} 秒")
    print(f"  • 详细报告: {report_file}")
    
    # 判断是否需要验证
    if args.verify and migration_stats['error_apps'] == 0:
        print("\n🔍 开始验证迁移结果...")
        verify_migration(args, app_list)
    
    return migration_stats['error_apps'] == 0

def verify_migration(args, app_list=None):
    """验证迁移结果"""
    print("🔍 开始验证迁移结果...")
    
    # 创建验证器
    verifier = MigrationVerifier()
    
    # 获取应用列表
    if app_list is None:
        if args.apps:
            app_list = args.apps.split(',')
        else:
            # 从迁移器获取应用列表
            migrator = VectorMigrator()
            app_list = migrator.get_all_app_aliases()
    
    if not app_list:
        print("❌ 没有找到需要验证的应用")
        return False
    
    # 执行验证
    verification_summary = verifier.verify_all_apps(app_list)
    
    # 保存验证报告
    report_file = verifier.save_verification_report(verification_summary)
    
    # 打印结果
    print("\n📊 验证结果:")
    print(f"  • 总应用数: {verification_summary['total_apps']}")
    print(f"  • 验证成功: {verification_summary['success_count']}")
    print(f"  • 验证失败: {verification_summary['error_count']}")
    print(f"  • 详细报告: {report_file}")
    
    return verification_summary['error_count'] == 0

def migrate_single(args):
    """迁移单个应用"""
    if not args.app:
        print("❌ 请指定要迁移的应用别名")
        return False
    
    print(f"🚀 开始迁移单个应用: {args.app}")
    
    # 创建迁移器
    migrator = VectorMigrator()
    
    # 执行迁移
    result = migrator.migrate_single_app(args.app)
    
    # 打印结果
    print(f"\n📊 迁移结果:")
    print(f"  • 应用: {result['app_alias']}")
    print(f"  • 状态: {'✅ 成功' if result['success'] else '❌ 失败'}")
    print(f"  • 操作: {result['action']}")
    print(f"  • 耗时: {result['elapsed_time']:.2f} 秒")
    
    if result['migration_stats']['errors']:
        print(f"  • 错误: {result['migration_stats']['errors']}")
    
    return result['success']

def show_status():
    """显示系统状态"""
    print("📊 系统状态检查...")

    # 检查配置
    print(f"✅ 配置文件: 正常")
    print(f"✅ DashScope API: {'已配置' if config.DASHSCOPE_API_KEY else '❌ 未配置'}")

    # 检查目录
    for name, path in config.OUTPUT_DIRS.items():
        exists = os.path.exists(path)
        print(f"{'✅' if exists else '❌'} {name}目录: {path}")

    # 检查数据库连接
    try:
        from sqlalchemy import create_engine

        db_url = config.get_database_url()
        engine = create_engine(db_url)
        with engine.connect():
            print("✅ 数据库连接: 正常")
    except Exception as e:
        print(f"❌ 数据库连接: 失败 - {str(e)}")

    # 获取应用列表
    try:
        migrator = VectorMigrator()
        app_list = migrator.get_all_app_aliases()
        print(f"✅ 发现应用: {len(app_list)} 个")
        if app_list:
            print(f"  应用列表: {', '.join(app_list[:10])}{'...' if len(app_list) > 10 else ''}")
    except Exception as e:
        print(f"❌ 获取应用列表失败: {str(e)}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='HelloDB向量数据库迁移工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 全量迁移所有应用
  python migrate.py migrate-all
  
  # 全量迁移并验证
  python migrate.py migrate-all --verify
  
  # 迁移指定应用
  python migrate.py migrate-all --apps "test,testdb,testnew2"
  
  # 迁移单个应用
  python migrate.py migrate-single --app test
  
  # 仅验证迁移结果
  python migrate.py verify
  
  # 检查系统状态
  python migrate.py status
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 全量迁移命令
    migrate_all_parser = subparsers.add_parser('migrate-all', help='全量迁移所有应用')
    migrate_all_parser.add_argument('--apps', type=str, help='指定要迁移的应用列表，逗号分隔')
    migrate_all_parser.add_argument('--verify', action='store_true', help='迁移完成后自动验证')
    
    # 单个迁移命令
    migrate_single_parser = subparsers.add_parser('migrate-single', help='迁移单个应用')
    migrate_single_parser.add_argument('--app', type=str, required=True, help='要迁移的应用别名')
    
    # 验证命令
    verify_parser = subparsers.add_parser('verify', help='验证迁移结果')
    verify_parser.add_argument('--apps', type=str, help='指定要验证的应用列表，逗号分隔')
    
    # 状态检查命令
    subparsers.add_parser('status', help='检查系统状态')
    
    args = parser.parse_args()
    
    # 打印横幅
    print_banner()
    print_config_info()
    
    # 执行命令
    success = True
    
    if args.command == 'migrate-all':
        success = migrate_all(args)
    elif args.command == 'migrate-single':
        success = migrate_single(args)
    elif args.command == 'verify':
        success = verify_migration(args)
    elif args.command == 'status':
        show_status()
    else:
        parser.print_help()
        return
    
    # 打印最终结果
    if args.command in ['migrate-all', 'migrate-single', 'verify']:
        print(f"\n🎯 执行结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            print("🎉 迁移任务完成！新系统已准备就绪。")
        else:
            print("⚠️  迁移过程中遇到问题，请检查日志文件。")

if __name__ == "__main__":
    main()
