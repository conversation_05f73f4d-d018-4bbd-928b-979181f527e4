#!/bin/bash

set -e

# 检查license文件
if [[ -n "${HELLODB_LICENSE_PATH}" ]]; then
  echo "使用自定义license路径: ${HELLODB_LICENSE_PATH}"
  if [[ ! -f "${HELLODB_LICENSE_PATH}" ]]; then
    echo "警告: 在指定路径 ${HELLODB_LICENSE_PATH} 未找到license文件"
  fi
else
  # 默认license路径
  DEFAULT_LICENSE_PATH="/app/data/license.lic"
  
  # 检查默认路径
  if [[ -f "${DEFAULT_LICENSE_PATH}" ]]; then
    export HELLODB_LICENSE_PATH="${DEFAULT_LICENSE_PATH}"
    echo "使用默认license路径: ${HELLODB_LICENSE_PATH}"
  else
    echo "警告: 在默认路径 ${DEFAULT_LICENSE_PATH} 未找到license文件"
  fi
fi

if [[ "${MIGRATION_ENABLED}" == "true" ]]; then
  echo "Running migrations"
  flask db upgrade
fi

if [[ "${INIT_SYSTEM}" == "true" ]]; then
  echo "Initializing system..."
  flask init-system
fi

if [[ "${MODE}" == "worker" ]]; then
  celery -A app.celery worker -P ${CELERY_WORKER_CLASS:-gevent} -c ${CELERY_WORKER_AMOUNT:-1} --loglevel ${LOG_LEVEL:-INFO} \
    -Q ${CELERY_QUEUES:-chatdb,mail} --logfile=/app/logs/celery_worker.log
elif [[ "${MODE}" == "beat" ]]; then
  celery -A app.celery beat --loglevel ${LOG_LEVEL:-INFO} --logfile=/app/logs/celery_beat.log
else
  if [[ "${DEBUG}" == "true" ]]; then
    flask run --host=${APP_BIND_ADDRESS:-0.0.0.0} --port=${PORT:-5001} --debug
  else
    gunicorn \
      --bind "${APP_BIND_ADDRESS:-0.0.0.0}:${PORT:-5001}" \
      --workers ${SERVER_WORKER_AMOUNT:-1} \
      --worker-class ${SERVER_WORKER_CLASS:-gevent} \
      --timeout ${GUNICORN_TIMEOUT:-200} \
      --log-file /app/logs/gunicorn.log \
      --log-level debug \
      --access-logfile /app/logs/access.log \
      --error-logfile /app/logs/error.log \
      --capture-output \
      --preload \
      app:app
  fi
fi