#!/usr/bin/env python3
"""
向量数据库迁移核心工具
"""
import os
import sys
import json
import time
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

# 添加项目路径到sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from vector_migration.config.migration_config import config
from core.hellodb.vector.chromadb_vectorsotre import ChromaDB_VectorStore

class VectorMigrator:
    """向量数据库迁移器"""
    
    def __init__(self):
        self.config = config
        self.logger = self._setup_logger()
        self.migration_stats = {
            'total_apps': 0,
            'migrated_apps': 0,
            'created_apps': 0,
            'error_apps': 0,
            'start_time': None,
            'end_time': None,
            'results': []
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('VectorMigrator')
        logger.setLevel(getattr(logging, self.config.LOG_CONFIG['level']))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        formatter = logging.Formatter(self.config.LOG_CONFIG['format'])
        
        # 控制台处理器
        if self.config.LOG_CONFIG['console_enabled']:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        # 文件处理器
        if self.config.LOG_CONFIG['file_enabled']:
            log_file = os.path.join(
                self.config.OUTPUT_DIRS['logs'],
                f"migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            )
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        getattr(self.logger, level.lower())(message)
    
    def get_all_app_aliases(self) -> List[str]:
        """获取所有应用别名"""
        try:
            # 直接使用sqlalchemy连接数据库
            from sqlalchemy import create_engine, text

            # 构建数据库连接URL
            db_config = self.config.DATABASE_CONFIG
            db_url = f"postgresql://{db_config['username']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"

            engine = create_engine(db_url)
            with engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT DISTINCT alias
                    FROM data_source_app
                    WHERE status = 'active'
                    ORDER BY alias
                """))
                return [row[0] for row in result.fetchall()]
        except Exception as e:
            self.log(f"获取应用列表失败: {str(e)}", "ERROR")
            return []
    
    def check_old_data_exists(self, app_alias: str) -> bool:
        """检查旧数据是否存在"""
        old_path = self.config.get_old_vector_path(app_alias)
        return os.path.exists(old_path)
    
    def extract_old_data(self, app_alias: str) -> Dict[str, List]:
        """提取旧向量数据"""
        old_path = self.config.get_old_vector_path(app_alias)
        
        try:
            import chromadb
            client = chromadb.PersistentClient(path=old_path)
            
            extracted_data = {
                'sql': [],
                'ddl': [],
                'documentation': []
            }
            
            # 提取各个collection的数据
            for collection_name in extracted_data.keys():
                try:
                    collection = client.get_collection(collection_name)
                    data = collection.get(include=['documents', 'metadatas'])
                    
                    if data['documents']:
                        for i, doc in enumerate(data['documents']):
                            metadata = data['metadatas'][i] if data['metadatas'] and i < len(data['metadatas']) else {}
                            extracted_data[collection_name].append({
                                'text': doc,
                                'metadata': metadata
                            })
                    
                    self.log(f"  提取 {collection_name}: {len(extracted_data[collection_name])} 个文档")
                    
                except Exception as e:
                    self.log(f"  提取 {collection_name} 失败: {str(e)}", "WARNING")
            
            return extracted_data
            
        except Exception as e:
            self.log(f"提取旧数据失败: {str(e)}", "ERROR")
            return {'sql': [], 'ddl': [], 'documentation': []}
    
    def create_new_vector_store(self, app_alias: str) -> Optional[ChromaDB_VectorStore]:
        """创建新的向量存储"""
        new_path = self.config.get_new_vector_path(app_alias)
        vector_config = {
            'path': new_path,
            'dashscope_api_key': self.config.DASHSCOPE_API_KEY
        }
        
        try:
            return ChromaDB_VectorStore(config=vector_config)
        except Exception as e:
            self.log(f"创建新向量存储失败: {str(e)}", "ERROR")
            return None
    
    def migrate_data(self, app_alias: str, extracted_data: Dict[str, List], new_store: ChromaDB_VectorStore) -> Dict[str, Any]:
        """迁移数据到新向量存储"""
        migration_result = {
            'sql': 0,
            'ddl': 0,
            'documentation': 0,
            'errors': []
        }
        
        # 迁移documentation数据
        if extracted_data['documentation']:
            try:
                for doc_data in extracted_data['documentation']:
                    new_store.add_documentation(doc_data['text'])
                migration_result['documentation'] = len(extracted_data['documentation'])
                self.log(f"  迁移documentation: {migration_result['documentation']} 个文档")
            except Exception as e:
                error_msg = f"迁移documentation失败: {str(e)}"
                migration_result['errors'].append(error_msg)
                self.log(f"  {error_msg}", "ERROR")
        
        # 迁移SQL数据
        if extracted_data['sql']:
            try:
                for sql_data in extracted_data['sql']:
                    # 这里需要根据实际的SQL数据结构调整
                    question = sql_data['metadata'].get('question', '')
                    sql = sql_data['text']
                    new_store.add_question_sql(question, sql)
                migration_result['sql'] = len(extracted_data['sql'])
                self.log(f"  迁移SQL: {migration_result['sql']} 个文档")
            except Exception as e:
                error_msg = f"迁移SQL失败: {str(e)}"
                migration_result['errors'].append(error_msg)
                self.log(f"  {error_msg}", "ERROR")
        
        # 迁移DDL数据
        if extracted_data['ddl']:
            try:
                for ddl_data in extracted_data['ddl']:
                    new_store.add_ddl(ddl_data['text'])
                migration_result['ddl'] = len(extracted_data['ddl'])
                self.log(f"  迁移DDL: {migration_result['ddl']} 个文档")
            except Exception as e:
                error_msg = f"迁移DDL失败: {str(e)}"
                migration_result['errors'].append(error_msg)
                self.log(f"  {error_msg}", "ERROR")
        
        return migration_result
    
    def migrate_single_app(self, app_alias: str) -> Dict[str, Any]:
        """迁移单个应用"""
        self.log(f"开始迁移应用: {app_alias}")
        
        result = {
            'app_alias': app_alias,
            'success': False,
            'action': 'unknown',
            'migration_stats': {
                'sql': 0,
                'ddl': 0,
                'documentation': 0,
                'errors': []
            },
            'elapsed_time': 0
        }
        
        start_time = time.time()
        
        try:
            # 检查旧数据是否存在
            has_old_data = self.check_old_data_exists(app_alias)
            
            if has_old_data:
                # 提取旧数据
                self.log(f"  提取旧数据...")
                extracted_data = self.extract_old_data(app_alias)
                
                total_docs = sum(len(data) for data in extracted_data.values())
                if total_docs == 0:
                    # 有目录但无数据，创建空向量存储
                    self.log(f"  旧目录存在但无数据，创建新的空向量存储...")
                    new_store = self.create_new_vector_store(app_alias)
                    if new_store:
                        result['success'] = True
                        result['action'] = 'created_empty'
                    else:
                        result['action'] = 'error'
                else:
                    # 有数据，执行迁移
                    self.log(f"  发现 {total_docs} 个文档，开始迁移...")
                    
                    # 创建新向量存储
                    new_store = self.create_new_vector_store(app_alias)
                    if not new_store:
                        result['action'] = 'error'
                        return result
                    
                    # 迁移数据
                    migration_stats = self.migrate_data(app_alias, extracted_data, new_store)
                    result['migration_stats'] = migration_stats
                    
                    # 判断迁移是否成功
                    if not migration_stats['errors']:
                        result['success'] = True
                        result['action'] = 'migrated'
                        self.log(f"  迁移成功")
                    else:
                        result['action'] = 'error'
                        self.log(f"  迁移失败: {migration_stats['errors']}", "ERROR")
            else:
                # 无旧数据，创建新的空向量存储
                self.log(f"  无旧数据，创建新的空向量存储...")
                new_store = self.create_new_vector_store(app_alias)
                if new_store:
                    result['success'] = True
                    result['action'] = 'created'
                    self.log(f"  创建成功")
                else:
                    result['action'] = 'error'
                    self.log(f"  创建失败", "ERROR")
        
        except Exception as e:
            result['action'] = 'error'
            error_msg = f"迁移应用 {app_alias} 时发生异常: {str(e)}"
            result['migration_stats']['errors'].append(error_msg)
            self.log(f"  {error_msg}", "ERROR")
        
        result['elapsed_time'] = time.time() - start_time
        return result

    def migrate_all_apps(self, app_list: Optional[List[str]] = None) -> Dict[str, Any]:
        """迁移所有应用"""
        if app_list is None:
            app_list = self.get_all_app_aliases()

        if not app_list:
            self.log("没有找到需要迁移的应用", "WARNING")
            return self.migration_stats

        self.migration_stats['total_apps'] = len(app_list)
        self.migration_stats['start_time'] = time.time()

        self.log(f"开始全量迁移 {len(app_list)} 个应用...")

        for i, app_alias in enumerate(app_list, 1):
            self.log(f"处理应用 {i}/{len(app_list)}: {app_alias}")

            try:
                result = self.migrate_single_app(app_alias)
                self.migration_stats['results'].append(result)

                if result['success']:
                    if result['action'] in ['migrated']:
                        self.migration_stats['migrated_apps'] += 1
                    elif result['action'] in ['created', 'created_empty']:
                        self.migration_stats['created_apps'] += 1
                else:
                    self.migration_stats['error_apps'] += 1

                # 添加延迟避免过载
                time.sleep(0.5)

            except Exception as e:
                self.migration_stats['error_apps'] += 1
                error_msg = f"处理应用 {app_alias} 时发生异常: {str(e)}"
                self.log(error_msg, "ERROR")

                self.migration_stats['results'].append({
                    'app_alias': app_alias,
                    'success': False,
                    'action': 'error',
                    'error': error_msg
                })

        self.migration_stats['end_time'] = time.time()
        self.migration_stats['elapsed_time'] = self.migration_stats['end_time'] - self.migration_stats['start_time']

        # 打印总结
        stats = self.migration_stats
        self.log(f"全量迁移完成!")
        self.log(f"总计: {stats['total_apps']} 个应用")
        self.log(f"迁移成功: {stats['migrated_apps']} 个")
        self.log(f"新建成功: {stats['created_apps']} 个")
        self.log(f"失败: {stats['error_apps']} 个")
        self.log(f"总耗时: {stats['elapsed_time']:.2f} 秒")

        return self.migration_stats

    def save_migration_report(self) -> str:
        """保存迁移报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = os.path.join(
            self.config.OUTPUT_DIRS['reports'],
            f'migration_report_{timestamp}.json'
        )

        report = {
            'migration_summary': self.migration_stats,
            'config': self.config.to_dict(),
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        self.log(f"迁移报告已保存到: {report_file}")
        return report_file
