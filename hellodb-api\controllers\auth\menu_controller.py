from flask import abort, jsonify
from flask_login import current_user, login_required
from flask_restful import marshal_with
from flask import current_app as app
from controllers.auth import api

from controllers.wraps import WebApiResource

from fields.menu_fields import menu_response_fields
from services.account_service import AccountService


class MenuApi(WebApiResource):

    @marshal_with(menu_response_fields)
    @login_required
    def get(self):
        account = current_user
        difyUrl = app.config.get('DIFY_URL')

        menu = [  
            # {  
            #     "id": 2,  
            #     "parentId": None,  
            #     "title": "分析页",  
            #     "icon": "DashboardOutlined",  
            #     "component": "/dashboard/analysis",  
            #     "path": "/dashboard/analysis",  
            #     "name": "DashboardAnalysis",  
            #     "keepAlive": True,  
            #     "locale": "menu.dashboard.analysis",  
            # },  
            {  
                "id": 1,  
                "parentId": None,  
                "title": "仪表盘",  
                "icon": "DashboardOutlined",  
                "component": "/dashboard/index",  
                # "redirect": "/dashboard/index",  
                "path": "/dashboard",  
                "name": "Dashboard",
                "keepAlive": True, 
                "locale": "menu.dashboard",  
            }
        ]

        datasource_menu = [
            {  
                "id": 80,  
                "parentId": None,  
                "title": "数据助手管理",  
                "icon": "BarsOutlined",  
                "component": "RouteView",  
                "path": "/datasource/management",  
                "name": "DatasourceManagement",  
                "keepAlive": True,  
                "locale": "menu.datasource.management",  
            },  
            {  
                "id": 82,  
                "parentId": 80,  
                "title": "数据源配置",  
                "component": "/datasource/config-guide",  
                "path": "/datasource/config-guide",  
                "name": "DatasourceConfigGuide",  
                "keepAlive": True,  
                "locale": "menu.datasource.config-guide",  
            },  
            {  
                "id": 83,  
                "parentId": 80,  
                "title": "数据库连接管理",  
                "component": "/datasource/connection/list",  
                "path": "/datasource/connection/list",  
                "name": "DatasourceConnectionManagement",  
                "keepAlive": True,  
                "locale": "menu.datasource.connection.management",  
            },
            {
                "id": 84,
                "parentId": 80,
                "title": '创建数据助手',
                "component": '/datasource/app/edit',
                "path": '/datasource/app/edit',
                "name": 'DataAssistantCreate',
                "keepAlive": True,
                "locale": 'menu.datasource.app.create'
            },
            {
                "id": 85,
                "parentId": 80,
                "title": '编辑数据助手',
                "hideInMenu": True,
                "component": '/datasource/app/edit',
                "path": '/datasource/app/edit/:id?',
                "name": 'DataAssistantEdit',
                "keepAlive": True,
                "locale": 'menu.datasource.app.create'
            },
            {
                "id": 88,
                "parentId": 80,
                "title": '数据助手详情',
                "hideInMenu": True,
                "component": '/datasource/app/detail',
                "path": '/datasource/app/detail/:id?',
                "name": 'DataAssistantDetail',
                "keepAlive": True,
                "locale": 'menu.datasource.app.detail'
            },
            {  
                "id": 86,  
                "parentId": 80,  
                "title": "数据应用管理",  
                "component": "/datasource/app/list",  
                "path": "/datasource/app/list",  
                "name": "DatasourceAppManagement",  
                "keepAlive": True,  
                "locale": "menu.datasource.app.management",  
            },  
            {  
                "id": 87,  
                "parentId": 80,  
                "title": "训练数据管理",  
                "component": "/datasource/traindata/list",  
                "path": "/datasource/traindata/list",  
                "name": "DatasourceTraindataManagement",  
                "keepAlive": True,  
                "locale": "menu.datasource.traindata.management",  
            },
            {  
                "id": 188,  
                "parentId": 80, 
                "title": "数据应用对话日志",
                "hideInMenu": True,
                "component": "/datasource/app/logs",  
                "path": "/datasource/app/logs/:id?",  
                "name": "DatasourceAppLogs",  
                "keepAlive": True,  
                "locale": "menu.datasource.app.logs",  
            }
        ]

        system_menu = [
            {
                "id": 100,
                "parentId": None,
                "title": "系统管理",
                "icon": "SettingOutlined",
                "component": "RouteView",
                "path": "/system",
                "name": "SystemManagement",
                "keepAlive": True,
                "locale": "menu.system.management"
            },
            {
                "id": 101,
                "parentId": 100,
                "title": "用户管理",
                "component": "/system/user/list",
                "path": "/system/user/list",
                "name": "UserManagement",
                "keepAlive": True,
                "locale": "menu.system.user.management"
            },
            {
                "id": 106,
                "parentId": 100,
                "title": "接口日志",
                "component": "/system/api/logs",
                "path": "/system/api/logs",
                "name": "ApiLogsManagement",
                "keepAlive": True,
                "locale": "menu.system.api.logs.management"
            },
            {
                "id": 107,
                "parentId": 100,
                "title": "对话消息管理",
                "component": "/system/message/list",
                "path": "/system/message/list",
                "name": "MessageListManagement",
                "keepAlive": True,
                "locale": "menu.system.message.list"
            },
            {
                "id": 109,
                "parentId": 100,
                "title": "许可证管理",
                "component": "/system/license",
                "path": "/system/license",
                "name": "LicenseManagement",
                "keepAlive": True,
                "locale": "menu.system.license.management"
            },
        ]

        profile_menu = [
            {
                "id": 18,
                "parentId": 15,
                "path": '/access/admin',
                "title": '管理员',
                "name": 'AccessAdmin',
                "component": '/access/admin',
                "locale": 'menu.access.admin',
            },
            {  
                "id": 36,  
                "parentId": None,
                "title": "个人中心",  
                "icon": "UserOutlined",  
                "component": "RouteView",  
                "redirect": "/account/settings",  
                "path": "/account",  
                "name": "Account",  
                "locale": "menu.account.center"  
            },  
            # {  
            #     "id": 37,  
            #     "parentId": 36,  
            #     "path": "/account/profile",  
            #     "title": "个人主页",  
            #     "name": "AccountProfile",  
            #     "component": "/account/profile",
            #     "locale": "menu.account.profile"  
            # },  
            {  
                "id": 38,  
                "parentId": 36,  
                "path": "/account/settings",  
                "title": "个人设置",  
                "name": "AccountSettings",  
                "component": "/account/settings",
                "locale": "menu.account.settings"
            },
            {  
                "id": 39,  
                "parentId": 36,  
                "path": "/account/apikey",  
                "title": "APIKEY管理",  
                "name": "APIKeyManagement",  
                "component": "/account/apikey",
                "locale": "menu.account.apikey.management"
            }
        ]

        # if account and account.name == 'maa7' or account.name == 'admin' or account.name == 'guoweiwei':
        if account and AccountService.has_role(account, 'ADMIN'):
            menu.extend(datasource_menu)
            menu.extend(system_menu)
            menu.extend(profile_menu)
        else:
            menu.extend(datasource_menu)
            menu.extend(profile_menu)
            

        return  {"code": 200, "msg": "菜单信息获取成功", "data": menu}


api.add_resource(MenuApi, '/menu')