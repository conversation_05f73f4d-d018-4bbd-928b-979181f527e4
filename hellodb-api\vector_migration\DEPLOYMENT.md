# 🚀 生产环境部署指南

## 📋 部署前准备

### 1. 环境要求
- Python 3.8+
- 必要的Python包: `chromadb`, `sqlalchemy`, `psycopg2-binary`
- 数据库连接权限
- DashScope API密钥

### 2. 文件清单
确保以下文件已上传到服务器：
```
vector_migration/
├── README.md              # 详细使用说明
├── DEPLOYMENT.md          # 本部署指南
├── migrate.py             # 主迁移脚本
├── quick_migrate.py       # 快速迁移脚本
├── deploy.sh              # 自动部署脚本
├── .gitignore             # Git忽略文件
├── config/
│   └── migration_config.py   # 配置文件
└── core/
    ├── migrator.py        # 迁移核心
    └── verifier.py        # 验证工具
```

## 🎯 快速部署（推荐）

### 方式一：使用快速迁移脚本
```bash
# 1. 进入项目目录
cd /path/to/hellodb-api

# 2. 设置环境变量
export DASHSCOPE_API_KEY="your-production-api-key"

# 3. 运行快速迁移
python vector_migration/quick_migrate.py
```

### 方式二：使用自动部署脚本
```bash
# 1. 进入项目目录
cd /path/to/hellodb-api

# 2. 设置环境变量
export DASHSCOPE_API_KEY="your-production-api-key"
export DB_HOST="your-db-host"
export DB_PASSWORD="your-password"

# 3. 运行部署脚本
chmod +x vector_migration/deploy.sh
./vector_migration/deploy.sh
```

## 🔧 手动部署

### 1. 设置环境变量
```bash
export DASHSCOPE_API_KEY="your-production-api-key"
export DB_HOST="your-db-host"
export DB_PORT="5432"
export DB_NAME="hellodb"
export DB_USER="postgres"
export DB_PASSWORD="your-password"
```

### 2. 检查系统状态
```bash
python vector_migration/migrate.py status
```

### 3. 执行迁移
```bash
# 全量迁移（推荐）
python vector_migration/migrate.py migrate-all --verify

# 或者分步执行
python vector_migration/migrate.py migrate-all
python vector_migration/migrate.py verify
```

## 📊 迁移策略建议

### 生产环境迁移步骤

1. **预迁移测试**
   ```bash
   # 先迁移1-2个测试应用
   python vector_migration/migrate.py migrate-all --apps "test" --verify
   ```

2. **小批量迁移**
   ```bash
   # 迁移少量重要应用
   python vector_migration/migrate.py migrate-all --apps "app1,app2,app3" --verify
   ```

3. **全量迁移**
   ```bash
   # 确认无误后执行全量迁移
   python vector_migration/migrate.py migrate-all --verify
   ```

### 时间安排建议
- **最佳时间**: 业务低峰期（如凌晨2-6点）
- **预计耗时**: 45个应用约需30-60分钟
- **监控时间**: 迁移后24小时内密切监控

## 🛡️ 安全措施

### 1. 数据备份
- ✅ 迁移工具会自动保留原数据
- ✅ 建议额外创建数据库备份
- ✅ 确保有足够的磁盘空间

### 2. 回滚准备
```bash
# 如需回滚，可以：
# 1. 停止新系统
# 2. 恢复旧系统配置
# 3. 检查问题后重新迁移
```

### 3. 监控检查
```bash
# 迁移后检查
python vector_migration/migrate.py verify

# 查看日志
tail -f vector_migration/logs/migration_*.log
```

## 📁 输出文件说明

### 日志文件
- **位置**: `vector_migration/logs/migration_YYYYMMDD_HHMMSS.log`
- **内容**: 详细的迁移过程记录
- **用途**: 问题排查和审计

### 报告文件
- **迁移报告**: `vector_migration/temp/reports/migration_report_*.json`
- **验证报告**: `vector_migration/temp/reports/verification_report_*.json`
- **内容**: 统计信息和结果详情

## 🐛 常见问题处理

### 1. API连接问题
```bash
# 检查API密钥
echo $DASHSCOPE_API_KEY

# 测试网络连接
curl -H "Authorization: Bearer $DASHSCOPE_API_KEY" \
     https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding-v1
```

### 2. 数据库连接问题
```bash
# 检查数据库连接
python -c "
from vector_migration.config.migration_config import config
print('数据库URL:', config.get_database_url())
"
```

### 3. 权限问题
```bash
# 检查目录权限
ls -la vector_migration/
mkdir -p vector_migration/logs vector_migration/temp/reports
```

### 4. 迁移中断恢复
```bash
# 迁移工具支持重复运行，可以直接重新执行
python vector_migration/migrate.py migrate-all --verify
```

## 📞 应急联系

### 迁移失败处理流程
1. **立即停止**: 如发现严重问题，立即停止迁移
2. **保留现场**: 不要删除任何日志和临时文件
3. **收集信息**: 
   ```bash
   # 收集系统信息
   python vector_migration/migrate.py status > system_status.txt
   
   # 收集日志
   tar -czf migration_logs.tar.gz vector_migration/logs/
   ```
4. **联系技术支持**: 提供收集的信息

### 验证清单
- [ ] 所有应用迁移成功
- [ ] 验证测试通过
- [ ] 检索功能正常
- [ ] 性能指标正常
- [ ] 日志无严重错误
- [ ] 用户验收测试通过

## 🎉 迁移完成后

### 1. 系统切换
- 更新应用配置指向新系统
- 逐步切换用户流量
- 监控系统运行状态

### 2. 清理工作
- 确认新系统稳定运行1周后
- 可以考虑清理旧系统数据
- 保留迁移日志和报告

### 3. 文档更新
- 更新系统架构文档
- 更新运维手册
- 培训相关人员

---

**重要提醒**: 
- 🔴 迁移前务必确保有完整备份
- 🟡 建议在测试环境先完整验证
- 🟢 迁移过程中保持通讯畅通

**技术支持**: HelloDB开发团队  
**文档版本**: 1.0.0  
**更新时间**: 2025-07-21
