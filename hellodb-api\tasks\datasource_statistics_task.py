from models.datasource import DataSourceApp, DataSourceAppStatistics, ChatdbMessage
from extensions.ext_database import db
from datetime import datetime
import uuid
from celery import shared_task
from celery.schedules import crontab
from services.datasource_service import DatasourceService
from core.vanna.hellodb_vanna import HellodbVanna
from core.vanna.hellodb_vanna_factory import HellodbVannaFactory
from config import Config
from openai import OpenAI
import logging

def get_vanna_client(datasourceApp: DataSourceApp = None) -> HellodbVanna:
    """获取Vanna客户端"""
    config = Config()
    VANNA_LLM_MODEL = config.VANNA_LLM_MODEL
    VANNA_LLM_API_KEY = config.VANNA_LLM_API_KEY
    VANNA_LLM_BASE_URL = config.VANNA_LLM_BASE_URL

    if datasourceApp is None:
        path = '/data/chroma/default'
        initial_prompt = ''
    else:
        path = f'/data/chroma/{datasourceApp.alias}'
        initial_prompt = datasourceApp.initial_prompt

    vanna_config = {
        'path': path,
        'model': VANNA_LLM_MODEL,
        'api_key': VANNA_LLM_API_KEY,
        'initial_prompt': initial_prompt,
        'language': '中文'
    }

    client = OpenAI(api_key=VANNA_LLM_API_KEY, base_url=VANNA_LLM_BASE_URL)
    vn = HellodbVannaFactory.create_vanna(datasourceApp, client=client, config=vanna_config)
    return vn

def get_training_data_count(app: DataSourceApp) -> int:
    """获取应用的训练数据数量"""
    try:
        vn = get_vanna_client(app)
        df = vn.get_training_data()
        return len(df) if df is not None else 0
    except Exception as e:
        print(f"获取训练数据数量失败: {str(e)}")
        return 0

def get_table_count(app: DataSourceApp) -> int:
    """获取应用的数据库表数量"""
    try:
        vn = get_vanna_client(app)
        # 获取数据库摘要，其中包含表信息
        # FIXME 这里还有bug，不对哦，get_database_summary接受不了datasource_id
        datasource = DatasourceService.get_datasource_by_id(app.datasource_id)
        vn.connect_to_database(datasource)
        database_summary = vn.get_database_summary(app.datasource_id)
        # 解析数据库摘要中的表数量
        # 这里假设数据库摘要中包含了表的信息，需要根据实际情况调整
        tables = [line for line in database_summary.split('\n') if 'table ' in line]
        return len(tables)
    except Exception as e:
        print(f"获取表数量失败: {str(e)}")
        return 0

@shared_task(queue='chatdb')
def update_app_statistics(app_id: str) -> dict:
    """
    更新单个应用的统计数据
    
    Args:
        app_id: 应用ID
        
    Returns:
        dict: 更新后的统计数据
    """
    try:
        # 获取应用信息
        app = DatasourceService.get_app_by_id(app_id)
        if not app:
            return {"error": "应用不存在"}
            
        # 获取或创建统计记录
        statistics = DataSourceAppStatistics.query.filter_by(app_id=app_id).first()
        if not statistics:
            statistics = DataSourceAppStatistics(
                app_id=app_id,
                table_count=0,
                training_data_count=0,
                message_count=0,
                success_message_count=0
            )
            db.session.add(statistics)
            
        # 计算统计数据
        # 1. 获取消息总数
        message_count = ChatdbMessage.query.filter_by(app_id=app_id).count()
        # 2. 获取成功消息数
        success_message_count = ChatdbMessage.query.filter_by(
            app_id=app_id,
            answer_sql_valid=True
        ).count()
        # 3. 获取训练数据数量
        training_data_count = get_training_data_count(app)
        # 4. 获取表数量
        table_count = get_table_count(app)
        
        # 更新统计数据
        statistics.message_count = message_count
        statistics.success_message_count = success_message_count
        statistics.training_data_count = training_data_count
        statistics.table_count = table_count
        
        db.session.commit()
        return statistics.to_dict()
        
    except Exception as e:
        db.session.rollback()
        logging.error(f"更新应用统计数据失败: {str(e)}")
        return {"error": str(e)}

@shared_task(queue='chatdb')
def update_all_apps_statistics() -> dict:
    """
    更新所有应用的统计数据
    
    Returns:
        dict: 更新结果统计
    """
    try:
        logging.warning("调度任务，开始更新所有应用统计数据")
        apps = DataSourceApp.query.filter_by(status='active').all()
        results = {
            "total": len(apps),
            "success": 0,
            "failed": 0,
            "errors": []
        }
        
        for app in apps:
            try:
                update_app_statistics.delay(str(app.id))  # 使用delay异步执行
                results["success"] += 1
            except Exception as e:
                results["failed"] += 1
                results["errors"].append({
                    "app_id": str(app.id),
                    "error": str(e)
                })
                
        return results
        
    except Exception as e:
        logging.error(f"更新所有应用统计数据失败: {str(e)}")
        return {"error": str(e)} 