{"app_alias": "testnew2", "backup_time": "20250720_144944", "source_path": "/data/chroma/testnew2", "collections": {"sql": {"count": 0, "documents": [], "metadatas": [], "ids": [], "checksum": "38a5cc17c0838b3cb7fba5f14c5f3887"}, "ddl": {"count": 0, "documents": [], "metadatas": [], "ids": [], "checksum": "38a5cc17c0838b3cb7fba5f14c5f3887"}, "documentation": {"count": 17, "documents": ["table account_integrates: encrypted_token、account_id、updated_at、provider、id、open_id、created_at;\ntable account_permissions: id、resource_id、updated_at、resource_type、created_at、permisson_type、account_id;\ntable accounts: name(用户名)、nickname(昵称)、email(邮箱)、email_verified_at(邮箱验证时间)、mobile(手机号)、password(密码)、password_salt(密码盐)、avatar(头像)、region(地区)、interface_language(界面语言)、interface_theme(界面主题)、timezone(时区)、last_login_at(最后登录时间)、last_login_ip(最后登录IP)、last_active_at(最后活跃时间)、status(状态)、initialized_at(初始化时间)、created_at(创建时间)、updated_at(更新时间)、id;\ntable alembic_version: version_num;\ntable chatdb_message: id(主键)、app_id(应用ID)、question(用户问题)、answer_text(回复消息)、answer_sql(回复SQL语句)、answer_sql_valid(SQL语句是否有效)、answer_data(回复数据)、answer_data_row_count(回复数据行数)、answer_charts(回复图表代码)、from_source(来源)、created_at(创建时间)、created_by(创建人)、is_recommend(是否推荐)、recommend_at(推荐时间)、recommend_by(推荐人);\ntable data_source_app: id(主键)、name(应用名称)、alias(应用别名)、description(应用描述)、datasource_id(数据源ID)、datasource_type(数据源类型)、created_at(创建时间)、created_by(创建人)、updated_at(更新时间)、updated_by(更新人)、initial_prompt(初始提示词)、app_key(应用密钥)、status(状态)、check_results(检测结果)、last_check_time(上次检测时间);\ntable data_source_app_statistics: id(主键)、app_id(应用ID)、table_count(表数)、training_data_count(训练数据量)、message_count(消息数)、success_message_count(成功消息数);\ntable datasources: id(主键)、name(数据源名称)、type(数据源类型)、host(数据源主机地址)、port(数据源端口)、database(数据源数据库名称)、username(数据源用户名)、password(数据源密码加密字段)、created_at(创建时间)、created_by(创建人)、updated_at(更新时间)、updated_by(更新人)、ssl(是否启用SSL连接)、dsn(数据源DSN)、tds_version(数据源TDS版本);\ntable http_request_logs: id(主键ID)、account_id(用户账号ID)、ip_address(请求IP地址)、path(请求路径)、method(请求方法)、request_data(请求数据(包含请求参数和请求体))、status_code(响应状态码)、response_size(响应内容大小(字节))、response_error_data(响应错误内容)、user_agent(用户代理(浏览器/应用信息))、os_info(操作系统信息)、referer(请求来源页面)、duration_ms(请求处理时间(毫秒))、created_at(创建时间);\ntable invitation_codes: id、status、deprecated_at、code、created_at、used_by_account_id、used_at、batch;\ntable sys_departments: name(部门名称)、code(部门编码)、leader(负责人)、phone(联系电话)、email(邮箱)、sort(排序)、parent_id(父级ID)、status(状态：active-启用，inactive-禁用)、created_at(创建时间)、updated_at(更新时间)、id;\ntable sys_menus: name(菜单名称)、path(路由路径)、icon(图标)、sort(排序)、type(类型：menu-菜单，button-按钮)、permission(权限标识)、parent_id(父级ID)、status(状态：active-启用，inactive-禁用)、created_at(创建时间)、updated_at(更新时间)、id;\ntable sys_permissions: name(权限名称)、code(权限标识)、type(类型：menu-菜单，button-按钮)、status(状态：active-启用，inactive-禁用)、created_at(创建时间)、updated_at(更新时间)、id;\ntable sys_role_permissions: created_at(创建时间)、permission_id、id、role_id;\ntable sys_roles: name(角色名称)、code(角色编码)、description(描述)、status(状态：active-启用，inactive-禁用)、created_at(创建时间)、updated_at(更新时间)、id;\ntable sys_users: username(用户名)、nickname(昵称)、email(邮箱)、phone(手机号)、department_id(部门ID)、role_id(角色ID)、status(状态：active-启用，inactive-禁用)、created_at(创建时间)、updated_at(更新时间)、id", "The following columns are in the accounts table in the hellodb database:\n\n|     | table_catalog   | table_schema   | table_name   | column_name        | data_type                   | column_comment   |\n|----:|:----------------|:---------------|:-------------|:-------------------|:----------------------------|:-----------------|\n|   0 | hellodb         | public         | accounts     | name               | character varying           | 用户名           |\n|   1 | hellodb         | public         | accounts     | nickname           | character varying           | 昵称             |\n|   2 | hellodb         | public         | accounts     | email              | character varying           | 邮箱             |\n|   3 | hellodb         | public         | accounts     | email_verified_at  | timestamp without time zone | 邮箱验证时间     |\n|   4 | hellodb         | public         | accounts     | mobile             | character varying           | 手机号           |\n|   5 | hellodb         | public         | accounts     | password           | character varying           | 密码             |\n|   6 | hellodb         | public         | accounts     | password_salt      | character varying           | 密码盐           |\n|   7 | hellodb         | public         | accounts     | avatar             | character varying           | 头像             |\n|   8 | hellodb         | public         | accounts     | region             | character varying           | 地区             |\n|   9 | hellodb         | public         | accounts     | interface_language | character varying           | 界面语言         |\n|  10 | hellodb         | public         | accounts     | interface_theme    | character varying           | 界面主题         |\n|  11 | hellodb         | public         | accounts     | timezone           | character varying           | 时区             |\n|  12 | hellodb         | public         | accounts     | last_login_at      | timestamp without time zone | 最后登录时间     |\n|  13 | hellodb         | public         | accounts     | last_login_ip      | character varying           | 最后登录IP       |\n|  14 | hellodb         | public         | accounts     | last_active_at     | timestamp without time zone | 最后活跃时间     |\n|  15 | hellodb         | public         | accounts     | status             | character varying           | 状态             |\n|  16 | hellodb         | public         | accounts     | initialized_at     | timestamp without time zone | 初始化时间       |\n|  17 | hellodb         | public         | accounts     | created_at         | timestamp without time zone | 创建时间         |\n|  18 | hellodb         | public         | accounts     | updated_at         | timestamp without time zone | 更新时间         |\n| 136 | hellodb         | public         | accounts     | id                 | uuid                        |                  |", "The following columns are in the datasources table in the hellodb database:\n\n|     | table_catalog   | table_schema   | table_name   | column_name   | data_type                   | column_comment     |\n|----:|:----------------|:---------------|:-------------|:--------------|:----------------------------|:-------------------|\n|  19 | hellodb         | public         | datasources  | id            | uuid                        | 主键               |\n|  20 | hellodb         | public         | datasources  | name          | character varying           | 数据源名称         |\n|  21 | hellodb         | public         | datasources  | type          | character varying           | 数据源类型         |\n|  22 | hellodb         | public         | datasources  | host          | character varying           | 数据源主机地址     |\n|  23 | hellodb         | public         | datasources  | port          | integer                     | 数据源端口         |\n|  24 | hellodb         | public         | datasources  | database      | character varying           | 数据源数据库名称   |\n|  25 | hellodb         | public         | datasources  | username      | character varying           | 数据源用户名       |\n|  26 | hellodb         | public         | datasources  | password      | bytea                       | 数据源密码加密字段 |\n|  27 | hellodb         | public         | datasources  | created_at    | timestamp without time zone | 创建时间           |\n|  28 | hellodb         | public         | datasources  | created_by    | character varying           | 创建人             |\n|  29 | hellodb         | public         | datasources  | updated_at    | timestamp without time zone | 更新时间           |\n|  30 | hellodb         | public         | datasources  | updated_by    | character varying           | 更新人             |\n| 123 | hellodb         | public         | datasources  | ssl           | boolean                     | 是否启用SSL连接    |\n| 124 | hellodb         | public         | datasources  | dsn           | character varying           | 数据源DSN          |\n| 125 | hellodb         | public         | datasources  | tds_version   | character varying           | 数据源TDS版本      |", "The following columns are in the http_request_logs table in the hellodb database:\n\n|    | table_catalog   | table_schema   | table_name        | column_name         | data_type                | column_comment                 |\n|---:|:----------------|:---------------|:------------------|:--------------------|:-------------------------|:-------------------------------|\n| 31 | hellodb         | public         | http_request_logs | id                  | uuid                     | 主键ID                         |\n| 32 | hellodb         | public         | http_request_logs | account_id          | uuid                     | 用户账号ID                     |\n| 33 | hellodb         | public         | http_request_logs | ip_address          | character varying        | 请求IP地址                     |\n| 34 | hellodb         | public         | http_request_logs | path                | character varying        | 请求路径                       |\n| 35 | hellodb         | public         | http_request_logs | method              | character varying        | 请求方法                       |\n| 36 | hellodb         | public         | http_request_logs | request_data        | text                     | 请求数据(包含请求参数和请求体) |\n| 37 | hellodb         | public         | http_request_logs | status_code         | integer                  | 响应状态码                     |\n| 38 | hellodb         | public         | http_request_logs | response_size       | integer                  | 响应内容大小(字节)             |\n| 39 | hellodb         | public         | http_request_logs | response_error_data | text                     | 响应错误内容                   |\n| 40 | hellodb         | public         | http_request_logs | user_agent          | character varying        | 用户代理(浏览器/应用信息)      |\n| 41 | hellodb         | public         | http_request_logs | os_info             | character varying        | 操作系统信息                   |\n| 42 | hellodb         | public         | http_request_logs | referer             | character varying        | 请求来源页面                   |\n| 43 | hellodb         | public         | http_request_logs | duration_ms         | integer                  | 请求处理时间(毫秒)             |\n| 44 | hellodb         | public         | http_request_logs | created_at          | timestamp with time zone | 创建时间                       |", "The following columns are in the sys_departments table in the hellodb database:\n\n|     | table_catalog   | table_schema   | table_name      | column_name   | data_type                   | column_comment                   |\n|----:|:----------------|:---------------|:----------------|:--------------|:----------------------------|:---------------------------------|\n|  45 | hellodb         | public         | sys_departments | name          | character varying           | 部门名称                         |\n|  46 | hellodb         | public         | sys_departments | code          | character varying           | 部门编码                         |\n|  47 | hellodb         | public         | sys_departments | leader        | character varying           | 负责人                           |\n|  48 | hellodb         | public         | sys_departments | phone         | character varying           | 联系电话                         |\n|  49 | hellodb         | public         | sys_departments | email         | character varying           | 邮箱                             |\n|  50 | hellodb         | public         | sys_departments | sort          | integer                     | 排序                             |\n|  51 | hellodb         | public         | sys_departments | parent_id     | uuid                        | 父级ID                           |\n|  52 | hellodb         | public         | sys_departments | status        | character varying           | 状态：active-启用，inactive-禁用 |\n|  53 | hellodb         | public         | sys_departments | created_at    | timestamp without time zone | 创建时间                         |\n|  54 | hellodb         | public         | sys_departments | updated_at    | timestamp without time zone | 更新时间                         |\n| 130 | hellodb         | public         | sys_departments | id            | uuid                        |                                  |", "The following columns are in the sys_menus table in the hellodb database:\n\n|     | table_catalog   | table_schema   | table_name   | column_name   | data_type                   | column_comment                   |\n|----:|:----------------|:---------------|:-------------|:--------------|:----------------------------|:---------------------------------|\n|  55 | hellodb         | public         | sys_menus    | name          | character varying           | 菜单名称                         |\n|  56 | hellodb         | public         | sys_menus    | path          | character varying           | 路由路径                         |\n|  57 | hellodb         | public         | sys_menus    | icon          | character varying           | 图标                             |\n|  58 | hellodb         | public         | sys_menus    | sort          | integer                     | 排序                             |\n|  59 | hellodb         | public         | sys_menus    | type          | character varying           | 类型：menu-菜单，button-按钮     |\n|  60 | hellodb         | public         | sys_menus    | permission    | character varying           | 权限标识                         |\n|  61 | hellodb         | public         | sys_menus    | parent_id     | uuid                        | 父级ID                           |\n|  62 | hellodb         | public         | sys_menus    | status        | character varying           | 状态：active-启用，inactive-禁用 |\n|  63 | hellodb         | public         | sys_menus    | created_at    | timestamp without time zone | 创建时间                         |\n|  64 | hellodb         | public         | sys_menus    | updated_at    | timestamp without time zone | 更新时间                         |\n| 149 | hellodb         | public         | sys_menus    | id            | uuid                        |                                  |", "The following columns are in the sys_permissions table in the hellodb database:\n\n|     | table_catalog   | table_schema   | table_name      | column_name   | data_type                   | column_comment                   |\n|----:|:----------------|:---------------|:----------------|:--------------|:----------------------------|:---------------------------------|\n|  65 | hellodb         | public         | sys_permissions | name          | character varying           | 权限名称                         |\n|  66 | hellodb         | public         | sys_permissions | code          | character varying           | 权限标识                         |\n|  67 | hellodb         | public         | sys_permissions | type          | character varying           | 类型：menu-菜单，button-按钮     |\n|  68 | hellodb         | public         | sys_permissions | status        | character varying           | 状态：active-启用，inactive-禁用 |\n|  69 | hellodb         | public         | sys_permissions | created_at    | timestamp without time zone | 创建时间                         |\n|  70 | hellodb         | public         | sys_permissions | updated_at    | timestamp without time zone | 更新时间                         |\n| 138 | hellodb         | public         | sys_permissions | id            | uuid                        |                                  |", "The following columns are in the sys_roles table in the hellodb database:\n\n|     | table_catalog   | table_schema   | table_name   | column_name   | data_type                   | column_comment                   |\n|----:|:----------------|:---------------|:-------------|:--------------|:----------------------------|:---------------------------------|\n|  71 | hellodb         | public         | sys_roles    | name          | character varying           | 角色名称                         |\n|  72 | hellodb         | public         | sys_roles    | code          | character varying           | 角色编码                         |\n|  73 | hellodb         | public         | sys_roles    | description   | character varying           | 描述                             |\n|  74 | hellodb         | public         | sys_roles    | status        | character varying           | 状态：active-启用，inactive-禁用 |\n|  75 | hellodb         | public         | sys_roles    | created_at    | timestamp without time zone | 创建时间                         |\n|  76 | hellodb         | public         | sys_roles    | updated_at    | timestamp without time zone | 更新时间                         |\n| 154 | hellodb         | public         | sys_roles    | id            | uuid                        |                                  |", "The following columns are in the data_source_app table in the hellodb database:\n\n|     | table_catalog   | table_schema   | table_name      | column_name     | data_type                   | column_comment   |\n|----:|:----------------|:---------------|:----------------|:----------------|:----------------------------|:-----------------|\n|  77 | hellodb         | public         | data_source_app | id              | uuid                        | 主键             |\n|  78 | hellodb         | public         | data_source_app | name            | character varying           | 应用名称         |\n|  79 | hellodb         | public         | data_source_app | alias           | character varying           | 应用别名         |\n|  80 | hellodb         | public         | data_source_app | description     | text                        | 应用描述         |\n|  81 | hellodb         | public         | data_source_app | datasource_id   | uuid                        | 数据源ID         |\n|  82 | hellodb         | public         | data_source_app | datasource_type | character varying           | 数据源类型       |\n|  83 | hellodb         | public         | data_source_app | created_at      | timestamp without time zone | 创建时间         |\n|  84 | hellodb         | public         | data_source_app | created_by      | character varying           | 创建人           |\n|  85 | hellodb         | public         | data_source_app | updated_at      | timestamp without time zone | 更新时间         |\n|  86 | hellodb         | public         | data_source_app | updated_by      | character varying           | 更新人           |\n|  87 | hellodb         | public         | data_source_app | initial_prompt  | text                        | 初始提示词       |\n|  88 | hellodb         | public         | data_source_app | app_key         | character varying           | 应用密钥         |\n|  89 | hellodb         | public         | data_source_app | status          | character varying           | 状态             |\n| 121 | hellodb         | public         | data_source_app | check_results   | json                        | 检测结果         |\n| 122 | hellodb         | public         | data_source_app | last_check_time | timestamp without time zone | 上次检测时间     |", "The following columns are in the sys_role_permissions table in the hellodb database:\n\n|     | table_catalog   | table_schema   | table_name           | column_name   | data_type                   | column_comment   |\n|----:|:----------------|:---------------|:---------------------|:--------------|:----------------------------|:-----------------|\n|  90 | hellodb         | public         | sys_role_permissions | created_at    | timestamp without time zone | 创建时间         |\n| 140 | hellodb         | public         | sys_role_permissions | permission_id | uuid                        |                  |\n| 144 | hellodb         | public         | sys_role_permissions | id            | uuid                        |                  |\n| 148 | hellodb         | public         | sys_role_permissions | role_id       | uuid                        |                  |", "The following columns are in the sys_users table in the hellodb database:\n\n|     | table_catalog   | table_schema   | table_name   | column_name   | data_type                   | column_comment                   |\n|----:|:----------------|:---------------|:-------------|:--------------|:----------------------------|:---------------------------------|\n|  91 | hellodb         | public         | sys_users    | username      | character varying           | 用户名                           |\n|  92 | hellodb         | public         | sys_users    | nickname      | character varying           | 昵称                             |\n|  93 | hellodb         | public         | sys_users    | email         | character varying           | 邮箱                             |\n|  94 | hellodb         | public         | sys_users    | phone         | character varying           | 手机号                           |\n|  95 | hellodb         | public         | sys_users    | department_id | uuid                        | 部门ID                           |\n|  96 | hellodb         | public         | sys_users    | role_id       | uuid                        | 角色ID                           |\n|  97 | hellodb         | public         | sys_users    | status        | character varying           | 状态：active-启用，inactive-禁用 |\n|  98 | hellodb         | public         | sys_users    | created_at    | timestamp without time zone | 创建时间                         |\n|  99 | hellodb         | public         | sys_users    | updated_at    | timestamp without time zone | 更新时间                         |\n| 157 | hellodb         | public         | sys_users    | id            | uuid                        |                                  |", "The following columns are in the chatdb_message table in the hellodb database:\n\n|     | table_catalog   | table_schema   | table_name     | column_name           | data_type                   | column_comment   |\n|----:|:----------------|:---------------|:---------------|:----------------------|:----------------------------|:-----------------|\n| 100 | hellodb         | public         | chatdb_message | id                    | uuid                        | 主键             |\n| 101 | hellodb         | public         | chatdb_message | app_id                | uuid                        | 应用ID           |\n| 102 | hellodb         | public         | chatdb_message | question              | character varying           | 用户问题         |\n| 103 | hellodb         | public         | chatdb_message | answer_text           | text                        | 回复消息         |\n| 104 | hellodb         | public         | chatdb_message | answer_sql            | text                        | 回复SQL语句      |\n| 105 | hellodb         | public         | chatdb_message | answer_sql_valid      | boolean                     | SQL语句是否有效  |\n| 106 | hellodb         | public         | chatdb_message | answer_data           | text                        | 回复数据         |\n| 107 | hellodb         | public         | chatdb_message | answer_data_row_count | integer                     | 回复数据行数     |\n| 108 | hellodb         | public         | chatdb_message | answer_charts         | text                        | 回复图表代码     |\n| 109 | hellodb         | public         | chatdb_message | from_source           | character varying           | 来源             |\n| 110 | hellodb         | public         | chatdb_message | created_at            | timestamp without time zone | 创建时间         |\n| 111 | hellodb         | public         | chatdb_message | created_by            | character varying           | 创建人           |\n| 112 | hellodb         | public         | chatdb_message | is_recommend          | boolean                     | 是否推荐         |\n| 113 | hellodb         | public         | chatdb_message | recommend_at          | timestamp without time zone | 推荐时间         |\n| 114 | hellodb         | public         | chatdb_message | recommend_by          | character varying           | 推荐人           |", "The following columns are in the data_source_app_statistics table in the hellodb database:\n\n|     | table_catalog   | table_schema   | table_name                 | column_name           | data_type   | column_comment   |\n|----:|:----------------|:---------------|:---------------------------|:----------------------|:------------|:-----------------|\n| 115 | hellodb         | public         | data_source_app_statistics | id                    | uuid        | 主键             |\n| 116 | hellodb         | public         | data_source_app_statistics | app_id                | uuid        | 应用ID           |\n| 117 | hellodb         | public         | data_source_app_statistics | table_count           | integer     | 表数             |\n| 118 | hellodb         | public         | data_source_app_statistics | training_data_count   | integer     | 训练数据量       |\n| 119 | hellodb         | public         | data_source_app_statistics | message_count         | integer     | 消息数           |\n| 120 | hellodb         | public         | data_source_app_statistics | success_message_count | integer     | 成功消息数       |", "The following columns are in the alembic_version table in the hellodb database:\n\n|     | table_catalog   | table_schema   | table_name      | column_name   | data_type         | column_comment   |\n|----:|:----------------|:---------------|:----------------|:--------------|:------------------|:-----------------|\n| 126 | hellodb         | public         | alembic_version | version_num   | character varying |                  |", "The following columns are in the invitation_codes table in the hellodb database:\n\n|     | table_catalog   | table_schema   | table_name       | column_name        | data_type                   | column_comment   |\n|----:|:----------------|:---------------|:-----------------|:-------------------|:----------------------------|:-----------------|\n| 127 | hellodb         | public         | invitation_codes | id                 | integer                     |                  |\n| 131 | hellodb         | public         | invitation_codes | status             | character varying           |                  |\n| 134 | hellodb         | public         | invitation_codes | deprecated_at      | timestamp without time zone |                  |\n| 135 | hellodb         | public         | invitation_codes | code               | character varying           |                  |\n| 141 | hellodb         | public         | invitation_codes | created_at         | timestamp without time zone |                  |\n| 143 | hellodb         | public         | invitation_codes | used_by_account_id | uuid                        |                  |\n| 147 | hellodb         | public         | invitation_codes | used_at            | timestamp without time zone |                  |\n| 156 | hellodb         | public         | invitation_codes | batch              | character varying           |                  |", "The following columns are in the account_permissions table in the hellodb database:\n\n|     | table_catalog   | table_schema   | table_name          | column_name    | data_type                   | column_comment   |\n|----:|:----------------|:---------------|:--------------------|:---------------|:----------------------------|:-----------------|\n| 128 | hellodb         | public         | account_permissions | id             | uuid                        |                  |\n| 129 | hellodb         | public         | account_permissions | resource_id    | uuid                        |                  |\n| 132 | hellodb         | public         | account_permissions | updated_at     | timestamp without time zone |                  |\n| 137 | hellodb         | public         | account_permissions | resource_type  | character varying           |                  |\n| 142 | hellodb         | public         | account_permissions | created_at     | timestamp without time zone |                  |\n| 145 | hellodb         | public         | account_permissions | permisson_type | character varying           |                  |\n| 155 | hellodb         | public         | account_permissions | account_id     | uuid                        |                  |", "The following columns are in the account_integrates table in the hellodb database:\n\n|     | table_catalog   | table_schema   | table_name         | column_name     | data_type                   | column_comment   |\n|----:|:----------------|:---------------|:-------------------|:----------------|:----------------------------|:-----------------|\n| 133 | hellodb         | public         | account_integrates | encrypted_token | character varying           |                  |\n| 139 | hellodb         | public         | account_integrates | account_id      | uuid                        |                  |\n| 146 | hellodb         | public         | account_integrates | updated_at      | timestamp without time zone |                  |\n| 150 | hellodb         | public         | account_integrates | provider        | character varying           |                  |\n| 151 | hellodb         | public         | account_integrates | id              | uuid                        |                  |\n| 152 | hellodb         | public         | account_integrates | open_id         | character varying           |                  |\n| 153 | hellodb         | public         | account_integrates | created_at      | timestamp without time zone |                  |"], "metadatas": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "ids": ["1536a1f4-71b4-527c-8ec5-835f332e2da8-doc", "8b3bc252-3bdb-5e92-9514-a9796c319f8f-doc", "2f7a9184-cddd-52e1-8c37-f6e31a7aba9d-doc", "fb8c93c8-5a4b-5190-816b-a65ce0400cd8-doc", "11137b1e-9271-5dc9-9695-161749baab0f-doc", "ebead0b7-a13d-515e-8def-c37f0c768d65-doc", "941e82e9-817b-5dca-b012-bc0cf05d8955-doc", "4b04d7e8-ff00-575d-9c0d-5a33daf35a5a-doc", "61ad89a7-db74-54d9-abed-b6a40dc6ab93-doc", "a0b0ba1f-d5cf-50d3-aa97-ea1d4e627380-doc", "43ae7d41-ca80-5ed1-8573-1c186d7ef35a-doc", "a7d3b15a-1a9f-51bc-b740-44485041f9e3-doc", "bd192218-e2f7-5306-b22b-b8d0e7395c35-doc", "aad921fb-63b8-5944-aadc-c59af2c0330f-doc", "b78d9a83-0677-5522-ac19-6c420c1f6fd3-doc", "4bb49bc8-e6f7-5b12-bb81-d8d74803060e-doc", "bb559b65-cc69-5a6c-83a5-e6bb2df5ece9-doc"], "checksum": "ed8ca5b21cd7225a9454e134e6ef5cba"}}}