# base image
# FROM python:3.10-slim-bookworm AS base
FROM registry.cn-hangzhou.aliyuncs.com/cdnxy/python:3.10-slim-bookworm as base


LABEL maintainer="<EMAIL>"

# install packages
FROM base AS system-deps

# 配置阿里云的 APT 源，传统debian
# COPY sources.list /etc/apt/sources.list
# 替换debian源，DEB822格式 （Debian 12 以上的容器镜像）
COPY debian.sources.aliyun /etc/apt/sources.list.d/debian.sources

# 缓存 apt 包
RUN --mount=type=cache,target=/var/cache/apt \
    --mount=type=cache,target=/var/lib/apt/lists \
    apt-get update \
    && apt-get install -y --no-install-recommends gcc g++ libc-dev libffi-dev libgmp-dev libmpfr-dev libmpc-dev libsasl2-dev \
    && apt-get install -y --no-install-recommends curl wget \
    && apt-get install -y --no-install-recommends unixodbc-dev libltdl7 libodbc1 libodbc2 libodbccr2 odbcinst1debian2 curl gnupg \
    && apt-get autoremove \
    && apt-get clean

RUN curl -fsSL https://packages.microsoft.com/keys/microsoft.asc  | gpg --dearmor > /usr/share/keyrings/microsoft-archive-keyring.gpg \
    && echo "deb [signed-by=/usr/share/keyrings/microsoft-archive-keyring.gpg] https://packages.microsoft.com/debian/$(. /etc/os-release && echo $VERSION_ID)/prod $(. /etc/os-release && echo $VERSION_CODENAME) main" > /etc/apt/sources.list.d/mssql-release.list \
    && apt-get update \
    && ACCEPT_EULA=Y apt-get install -y msodbcsql17


# 第二阶段：安装 Python 依赖
FROM system-deps AS python-deps

# 配置 pip 镜像源
#RUN mkdir -p /root/.config/pip && \
#    echo "[global]" >> /root/.config/pip/pip.conf && \
#    echo "index-url = https://mirrors.aliyun.com/pypi/simple" >> /root/.config/pip/pip.conf
RUN mkdir -p /root/.config/pip
COPY pip.conf /root/.config/pip/pip.conf

COPY requirements.txt /requirements.txt

RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --prefix=/pkg -r requirements.txt


# production stage
FROM python-deps AS production

# 定义构建参数以支持动态 UID:GID
ARG UID=1000
ARG GID=1000

# 创建非 root 用户和组，使用动态 UID:GID
RUN groupadd -g ${GID} appuser && \
    useradd -u ${UID} -g ${GID} -m appuser

# 拷贝源文件
COPY --from=system-deps /etc/apt/sources.list.d/debian.sources /etc/apt/sources.list.d/debian.sources
COPY --from=system-deps /etc/apt/sources.list.d/mssql-release.list /etc/apt/sources.list.d/mssql-release.list
COPY --from=system-deps /usr/share/keyrings/microsoft-archive-keyring.gpg /usr/share/keyrings/

COPY --from=python-deps /pkg /usr/local


# 设置环境变量
ENV FLASK_APP=app.py \
    EDITION=SELF_HOSTED \
    DEPLOY_ENV=PRODUCTION \
    TZ=Asia/Shanghai \
    PYTHONPATH=/app/api \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1


# 创建目录结构和设置权限
RUN mkdir -p /app/storage /app/data /app/logs /app/chroma && \
    chown -R ${UID}:${GID} /app/storage /app/data /app/logs /app/chroma && \
    chmod -R 755 /app/storage /app/data /app/logs /app/chroma

WORKDIR /app/api

# 复制应用代码（使用 .dockerignore 排除不必要文件）
COPY --chown=${UID}:${GID} . /app/api/

# 复制并设置入口脚本
COPY --chmod=755 entrypoint.sh /entrypoint.sh

ARG COMMIT_SHA
ENV COMMIT_SHA=${COMMIT_SHA}

# 切换到非 root 用户
USER ${UID}:${GID}

# 暴露端口
EXPOSE 5001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5001/health || exit 1

ENTRYPOINT ["/bin/bash", "/entrypoint.sh"]