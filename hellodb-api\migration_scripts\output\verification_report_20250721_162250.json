{"verification_summary": {"total_apps": 3, "success_count": 3, "error_count": 0, "results": [{"app_alias": "test", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 114, "total_count": 114}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 5, "response_time": 1.7520246505737305, "has_results": true}, "查询": {"result_count": 3, "response_time": 0.5609900951385498, "has_results": true}, "表结构": {"result_count": 5, "response_time": 0.6018118858337402, "has_results": true}, "用户信息": {"result_count": 5, "response_time": 0.5890851020812988, "has_results": true}}, "overall_success": true}, {"app_alias": "testdb", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 0, "total_count": 0}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 0, "response_time": 0.21821880340576172, "has_results": false}, "查询": {"result_count": 0, "response_time": 0.32265782356262207, "has_results": false}, "表结构": {"result_count": 0, "response_time": 0.22157764434814453, "has_results": false}, "用户信息": {"result_count": 0, "response_time": 0.2669503688812256, "has_results": false}}, "overall_success": true}, {"app_alias": "testnew2", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 0, "total_count": 0}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 0, "response_time": 0.28525471687316895, "has_results": false}, "查询": {"result_count": 0, "response_time": 0.26461362838745117, "has_results": false}, "表结构": {"result_count": 0, "response_time": 0.20690631866455078, "has_results": false}, "用户信息": {"result_count": 0, "response_time": 0.23702216148376465, "has_results": false}}, "overall_success": true}]}, "verification_log": [{"timestamp": "2025-07-21 16:22:40", "level": "INFO", "message": "开始验证 3 个迁移的应用"}, {"timestamp": "2025-07-21 16:22:40", "level": "INFO", "message": "开始验证应用: test"}, {"timestamp": "2025-07-21 16:22:40", "level": "INFO", "message": "验证应用 test 的数据数量..."}, {"timestamp": "2025-07-21 16:22:41", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-21 16:22:41", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-21 16:22:41", "level": "INFO", "message": "  Documentation collection: 114 个文档"}, {"timestamp": "2025-07-21 16:22:41", "level": "INFO", "message": "测试应用 test 的检索功能..."}, {"timestamp": "2025-07-21 16:22:42", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-21 16:22:42", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-21 16:22:42", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-21 16:22:42", "level": "INFO", "message": "测试应用 test 的embedding质量..."}, {"timestamp": "2025-07-21 16:22:44", "level": "INFO", "message": "  查询 '数据库': 5 个结果, 响应时间: 1.752秒"}, {"timestamp": "2025-07-21 16:22:45", "level": "INFO", "message": "  查询 '查询': 3 个结果, 响应时间: 0.561秒"}, {"timestamp": "2025-07-21 16:22:45", "level": "INFO", "message": "  查询 '表结构': 5 个结果, 响应时间: 0.602秒"}, {"timestamp": "2025-07-21 16:22:46", "level": "INFO", "message": "  查询 '用户信息': 5 个结果, 响应时间: 0.589秒"}, {"timestamp": "2025-07-21 16:22:46", "level": "SUCCESS", "message": "应用 test 验证成功"}, {"timestamp": "2025-07-21 16:22:46", "level": "INFO", "message": "开始验证应用: testdb"}, {"timestamp": "2025-07-21 16:22:46", "level": "INFO", "message": "验证应用 testdb 的数据数量..."}, {"timestamp": "2025-07-21 16:22:46", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-21 16:22:46", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-21 16:22:46", "level": "INFO", "message": "  Documentation collection: 0 个文档"}, {"timestamp": "2025-07-21 16:22:46", "level": "INFO", "message": "测试应用 testdb 的检索功能..."}, {"timestamp": "2025-07-21 16:22:46", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-21 16:22:46", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-21 16:22:47", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-21 16:22:47", "level": "INFO", "message": "测试应用 testdb 的embedding质量..."}, {"timestamp": "2025-07-21 16:22:47", "level": "INFO", "message": "  查询 '数据库': 0 个结果, 响应时间: 0.218秒"}, {"timestamp": "2025-07-21 16:22:47", "level": "INFO", "message": "  查询 '查询': 0 个结果, 响应时间: 0.323秒"}, {"timestamp": "2025-07-21 16:22:48", "level": "INFO", "message": "  查询 '表结构': 0 个结果, 响应时间: 0.222秒"}, {"timestamp": "2025-07-21 16:22:48", "level": "INFO", "message": "  查询 '用户信息': 0 个结果, 响应时间: 0.267秒"}, {"timestamp": "2025-07-21 16:22:48", "level": "SUCCESS", "message": "应用 testdb 验证成功"}, {"timestamp": "2025-07-21 16:22:48", "level": "INFO", "message": "开始验证应用: testnew2"}, {"timestamp": "2025-07-21 16:22:48", "level": "INFO", "message": "验证应用 testnew2 的数据数量..."}, {"timestamp": "2025-07-21 16:22:48", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-21 16:22:48", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-21 16:22:48", "level": "INFO", "message": "  Documentation collection: 0 个文档"}, {"timestamp": "2025-07-21 16:22:48", "level": "INFO", "message": "测试应用 testnew2 的检索功能..."}, {"timestamp": "2025-07-21 16:22:48", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-21 16:22:49", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-21 16:22:49", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-21 16:22:49", "level": "INFO", "message": "测试应用 testnew2 的embedding质量..."}, {"timestamp": "2025-07-21 16:22:49", "level": "INFO", "message": "  查询 '数据库': 0 个结果, 响应时间: 0.285秒"}, {"timestamp": "2025-07-21 16:22:49", "level": "INFO", "message": "  查询 '查询': 0 个结果, 响应时间: 0.265秒"}, {"timestamp": "2025-07-21 16:22:50", "level": "INFO", "message": "  查询 '表结构': 0 个结果, 响应时间: 0.207秒"}, {"timestamp": "2025-07-21 16:22:50", "level": "INFO", "message": "  查询 '用户信息': 0 个结果, 响应时间: 0.237秒"}, {"timestamp": "2025-07-21 16:22:50", "level": "SUCCESS", "message": "应用 testnew2 验证成功"}, {"timestamp": "2025-07-21 16:22:50", "level": "INFO", "message": "验证完成: 成功=3, 失败=0"}], "generated_at": "2025-07-21 16:22:50"}