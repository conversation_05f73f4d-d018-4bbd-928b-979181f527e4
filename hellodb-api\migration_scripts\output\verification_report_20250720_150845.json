{"verification_summary": {"total_apps": 3, "success_count": 3, "error_count": 0, "results": [{"app_alias": "test", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 17, "total_count": 17}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 5, "response_time": 0.531597375869751, "has_results": true}, "查询": {"result_count": 4, "response_time": 0.5019652843475342, "has_results": true}, "表结构": {"result_count": 5, "response_time": 0.507556676864624, "has_results": true}, "用户信息": {"result_count": 4, "response_time": 0.5623319149017334, "has_results": true}}, "overall_success": true}, {"app_alias": "testdb", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 16, "total_count": 16}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 5, "response_time": 0.4982638359069824, "has_results": true}, "查询": {"result_count": 3, "response_time": 0.5239238739013672, "has_results": true}, "表结构": {"result_count": 5, "response_time": 0.5275657176971436, "has_results": true}, "用户信息": {"result_count": 3, "response_time": 0.629319429397583, "has_results": true}}, "overall_success": true}, {"app_alias": "testnew2", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 17, "total_count": 17}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 5, "response_time": 0.5886383056640625, "has_results": true}, "查询": {"result_count": 4, "response_time": 0.6050064563751221, "has_results": true}, "表结构": {"result_count": 5, "response_time": 0.5123412609100342, "has_results": true}, "用户信息": {"result_count": 4, "response_time": 0.4796879291534424, "has_results": true}}, "overall_success": true}]}, "verification_log": [{"timestamp": "2025-07-20 15:08:35", "level": "INFO", "message": "开始验证 3 个迁移的应用"}, {"timestamp": "2025-07-20 15:08:35", "level": "INFO", "message": "开始验证应用: test"}, {"timestamp": "2025-07-20 15:08:35", "level": "INFO", "message": "验证应用 test 的数据数量..."}, {"timestamp": "2025-07-20 15:08:35", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-20 15:08:35", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-20 15:08:35", "level": "INFO", "message": "  Documentation collection: 17 个文档"}, {"timestamp": "2025-07-20 15:08:35", "level": "INFO", "message": "测试应用 test 的检索功能..."}, {"timestamp": "2025-07-20 15:08:35", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-20 15:08:36", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-20 15:08:36", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-20 15:08:36", "level": "INFO", "message": "测试应用 test 的embedding质量..."}, {"timestamp": "2025-07-20 15:08:37", "level": "INFO", "message": "  查询 '数据库': 5 个结果, 响应时间: 0.532秒"}, {"timestamp": "2025-07-20 15:08:37", "level": "INFO", "message": "  查询 '查询': 4 个结果, 响应时间: 0.502秒"}, {"timestamp": "2025-07-20 15:08:38", "level": "INFO", "message": "  查询 '表结构': 5 个结果, 响应时间: 0.508秒"}, {"timestamp": "2025-07-20 15:08:38", "level": "INFO", "message": "  查询 '用户信息': 4 个结果, 响应时间: 0.562秒"}, {"timestamp": "2025-07-20 15:08:38", "level": "SUCCESS", "message": "应用 test 验证成功"}, {"timestamp": "2025-07-20 15:08:38", "level": "INFO", "message": "开始验证应用: testdb"}, {"timestamp": "2025-07-20 15:08:38", "level": "INFO", "message": "验证应用 testdb 的数据数量..."}, {"timestamp": "2025-07-20 15:08:38", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-20 15:08:38", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-20 15:08:38", "level": "INFO", "message": "  Documentation collection: 16 个文档"}, {"timestamp": "2025-07-20 15:08:38", "level": "INFO", "message": "测试应用 testdb 的检索功能..."}, {"timestamp": "2025-07-20 15:08:39", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-20 15:08:39", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-20 15:08:39", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-20 15:08:39", "level": "INFO", "message": "测试应用 testdb 的embedding质量..."}, {"timestamp": "2025-07-20 15:08:40", "level": "INFO", "message": "  查询 '数据库': 5 个结果, 响应时间: 0.498秒"}, {"timestamp": "2025-07-20 15:08:40", "level": "INFO", "message": "  查询 '查询': 3 个结果, 响应时间: 0.524秒"}, {"timestamp": "2025-07-20 15:08:41", "level": "INFO", "message": "  查询 '表结构': 5 个结果, 响应时间: 0.528秒"}, {"timestamp": "2025-07-20 15:08:42", "level": "INFO", "message": "  查询 '用户信息': 3 个结果, 响应时间: 0.629秒"}, {"timestamp": "2025-07-20 15:08:42", "level": "SUCCESS", "message": "应用 testdb 验证成功"}, {"timestamp": "2025-07-20 15:08:42", "level": "INFO", "message": "开始验证应用: testnew2"}, {"timestamp": "2025-07-20 15:08:42", "level": "INFO", "message": "验证应用 testnew2 的数据数量..."}, {"timestamp": "2025-07-20 15:08:42", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-20 15:08:42", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-20 15:08:42", "level": "INFO", "message": "  Documentation collection: 17 个文档"}, {"timestamp": "2025-07-20 15:08:42", "level": "INFO", "message": "测试应用 testnew2 的检索功能..."}, {"timestamp": "2025-07-20 15:08:42", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-20 15:08:43", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-20 15:08:43", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-20 15:08:43", "level": "INFO", "message": "测试应用 testnew2 的embedding质量..."}, {"timestamp": "2025-07-20 15:08:43", "level": "INFO", "message": "  查询 '数据库': 5 个结果, 响应时间: 0.589秒"}, {"timestamp": "2025-07-20 15:08:44", "level": "INFO", "message": "  查询 '查询': 4 个结果, 响应时间: 0.605秒"}, {"timestamp": "2025-07-20 15:08:45", "level": "INFO", "message": "  查询 '表结构': 5 个结果, 响应时间: 0.512秒"}, {"timestamp": "2025-07-20 15:08:45", "level": "INFO", "message": "  查询 '用户信息': 4 个结果, 响应时间: 0.480秒"}, {"timestamp": "2025-07-20 15:08:45", "level": "SUCCESS", "message": "应用 testnew2 验证成功"}, {"timestamp": "2025-07-20 15:08:45", "level": "INFO", "message": "验证完成: 成功=3, 失败=0"}], "generated_at": "2025-07-20 15:08:45"}