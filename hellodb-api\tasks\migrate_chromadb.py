import os
from core.hellodb.vector.chromadb_vectorsotre import ChromaDB_VectorStore


def migrate_chromadb(old_config, new_config):
    """
    Migrate data from the old ChromaDB vector store to the new one.
    
    Args:
        old_config (dict): Configuration for the old ChromaDB client.
        new_config (dict): Configuration for the new ChromaDB client.
    """
    # Initialize old and new vector stores
    old_store = ChromaDB_VectorStore(config=old_config)
    new_store = ChromaDB_VectorStore(config=new_config)

    # Migrate SQL collection
    print("Migrating SQL collection...")
    sql_data = old_store.sql_collection.get()
    if sql_data and "documents" in sql_data:
        for doc, meta, id in zip(sql_data["documents"], sql_data["metadatas"], sql_data["ids"]):
            doc_dict = json.loads(doc)
            new_store.add_question_sql(doc_dict["question"], doc_dict["sql"], doc_dict)

    # Migrate DDL collection
    print("Migrating DDL collection...")
    ddl_data = old_store.ddl_collection.get()
    if ddl_data and "documents" in ddl_data:
        for doc, meta, id in zip(ddl_data["documents"], ddl_data["metadatas"], ddl_data["ids"]):
            new_store.add_ddl(doc)

    # Migrate Documentation collection
    print("Migrating Documentation collection...")
    doc_data = old_store.documentation_collection.get()
    if doc_data and "documents" in doc_data:
        for doc, meta, id in zip(doc_data["documents"], doc_data["metadatas"], doc_data["ids"]):
            new_store.add_documentation(doc)

    print("Migration completed successfully!")


if __name__ == "__main__":
    # Example configuration for old and new ChromaDB clients
    old_config = {
        "path": "path_to_old_chromadb",
        "dashscope_api_key": "your_old_api_key"
    }
    new_config = {
        "path": "path_to_new_chromadb",
        "dashscope_api_key": "your_new_api_key"
    }

    migrate_chromadb(old_config, new_config)