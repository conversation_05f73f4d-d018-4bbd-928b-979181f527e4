import abc
import logging
import re
import pandas as pd
from typing import Optional, Union

from vanna.chromadb import ChromaDB_VectorStore
from vanna.qianwen import Qian<PERSON>enAI_Chat
from core.vanna.exception import HellodbTrainDataException
from models.datasource import Datasource
from vanna.qianwen  import QianWenAI_Chat

##############  向量检索的部分重写import   ##################
from typing import List, Dict, Any, Optional, Union
from llama_index.core import (
    VectorStoreIndex,
    Document,
    StorageContext,
    load_index_from_storage,
)
from llama_index.embeddings.modelscope import ModelScopeEmbedding
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.embeddings import BaseEmbedding
from llama_index.vector_stores.chroma import ChromaVectorStore

from llama_index.core.indices.vector_store.retrievers import VectorIndexRetriever
from llama_index.core.schema import QueryBundle, NodeWithScore

from llama_index.core.vector_stores.types import (
    MetadataFilter,
    MetadataFilters,
    VectorStoreQuery,
    VectorStoreQueryMode,
    VectorStoreQueryResult,
)
##############  向量检索的部分重写import   ##################

class HellodbVanna(ChromaDB_VectorStore, QianWenAI_Chat):
    """
    Base class for database operations with Vanna integration.
    Each database type should have its own implementation.
    """
    
    def __init__(self, client=None, config=None):
        logging.warning('HellodbVanna Initialized')
        if client:
            logging.warning('HellodbVanna base_url: %s', client.base_url)
        if config:
            logging.warning('HellodbVanna model: %s', config.get('model', 'Not specified'))
            logging.warning('HellodbVanna path: %s', config.get('path', 'Not specified'))
        
        ChromaDB_VectorStore.__init__(self, config=config)
        QianWenAI_Chat.__init__(self, client=client, config=config)
    
    @abc.abstractmethod
    def connect_to_database(self, datasource: Datasource) -> None:
        """
        Connect to a database using the provided datasource information.
        
        Args:
            datasource (Datasource): The datasource object containing connection information
        
        Returns:
            None
        
        Raises:
            Exception: If connection fails
        """
        pass
    
    @abc.abstractmethod
    def get_information_schema(self, database_name: Optional[str] = None) -> pd.DataFrame:
        """
        Retrieve the information schema for the connected database.
        
        Args:
            database_name (Optional[str]): The name of the database to get schema for.
                                          If None, uses the currently connected database.
        
        Returns:
            pd.DataFrame: DataFrame containing the information schema

            contains database | table_catalog、table_schema、 table_name、column_name、data_type、comment
        
        Raises:
            Exception: If not connected to a database or if retrieval fails
        """
        pass
    

    def get_database_summary(self, database_name: Optional[str] = None) -> str:
        """
        Retrieve the database summary for the connected database.
        
        Args:
            database_name (Optional[str]): The name of the database to get summary for
                                          If None, uses the currently connected database.
        
        Returns:
            str: The database summary,such as:
            tableA:id、title(商品标题)、price(价格)、total(数量)、create_time(下单日期)
            tableB:id、name(用户名)、phone(手机号)、email(邮箱)、create_time(注册日期)
            
        
        Raises:
            Exception: If not connected to a database or if retrieval fails
        """
        df = self.get_information_schema(database_name)

        if df.empty:
            return None
        
        # 判断是否有table_name列
        if 'table_name' not in df.columns:
            raise HellodbTrainDataException('table_name column not found in information schema')
        
        # 按表名分组处理数据
        result = []
        for table_name, group in df.groupby('table_name'):
            # 处理每一列的信息
            columns = []
            for _, row in group.iterrows():
                column_name = row['column_name']
                comment = row.get('column_comment', '')  # 使用get避免comment列不存在的情况
                
                # 如果有注释，添加括号说明，否则只使用列名
                if comment and not pd.isna(comment):
                    columns.append(f"{column_name}({comment})")
                else:
                    columns.append(column_name)
            
            # 将该表的所有列用顿号连接，并添加表名前缀
            table_summary = f"table {table_name}: {('、'.join(columns))}"
            result.append(table_summary)
        
        # 用分号和换行符连接所有表的信息
        return ';\n'.join(result)


    def update_training_data(self, id: str, question: str, sql: str, documentation: str, ddl: str) -> None:
        """
        Update training data with the provided question and SQL.
        
        Args:
            id (str): The ID of the training data to update
            question (str): The question to update
            sql (str): The SQL to update
            documentation (str): The documentation to update
            ddl (str): The DDL to update
        
        Returns:
            None
        
        Raises:
            Exception: If update fails
        """

        if id:
            self.remove_training_data(id)

        self.train(question, sql, ddl, documentation, None)


    def generate_echarts_code(self, question: str, sql: str, df: str, **kwargs) -> str:
        """
        Generate Echarts code from the provided question, SQL, and dataframe metadata.
        
        Args:
            question (str): The question to generate Echarts code for
            sql (str): The SQL query to generate Echarts code for
            df_metadata (str): The dataframe metadata to generate Echarts code for
            **kwargs: Additional keyword arguments to pass to the generation method
        
        Returns:
            str: The generated Echarts code
        
        Raises:
            Exception: If generation fails

        """
        if question is not None:
            system_msg = f"The following is a pandas DataFrame that contains the results of the query that answers the question the user asked: '{question}'"
        else:
            system_msg = "The following is a pandas DataFrame "

        if sql is not None:
            system_msg += f"\n\nThe DataFrame was produced using this query: {sql}\n\n"

        system_msg += f"The following is data 'df': \n{df}"

        message_log = [
            self.system_message(system_msg),
            self.user_message(
                "Can you generate the echarts option to chart the results of the dataframe?  If there is only one value in the df, use an Indicator. option code should include the data you can get from df. Respond with only json code. Do not answer with any explanations -- just the code."
            ),
        ]

        echarts_code = self.submit_prompt(message_log, kwargs=kwargs)

        return self._extract_json_code(echarts_code)
    

    def _extract_json_code(self, markdown_string: str) -> str:
        # Strip whitespace to avoid indentation errors in LLM-generated code
        markdown_string = markdown_string.strip()

        # Regex pattern to match json code blocks
        pattern = r"```[\w\s]*json\n([\s\S]*?)```|```([\s\S]*?)```|json\n([\s\S]*?)"

        # Find all matches in the markdown string
        matches = re.findall(pattern, markdown_string, re.IGNORECASE)

        # Extract the Python code from the matches
        json_code = []
        for match in matches:
            javascript = match[0] if match[0] else match[1]
            json_code.append(javascript.strip())

        if len(json_code) == 0:
            return markdown_string

        return json_code[0]

    def _extract_javascript_code(self, markdown_string: str) -> str:
        # Strip whitespace to avoid indentation errors in LLM-generated code
        markdown_string = markdown_string.strip()

        # Regex pattern to match Javascript code blocks
        pattern = r"```[\w\s]*javascript\n([\s\S]*?)```|```([\s\S]*?)```"

        # Find all matches in the markdown string
        matches = re.findall(pattern, markdown_string, re.IGNORECASE)

        # Extract the Python code from the matches
        javascript_code = []
        for match in matches:
            javascript = match[0] if match[0] else match[1]
            javascript_code.append(javascript.strip())

        if len(javascript_code) == 0:
            return markdown_string

        return javascript_code[0]


###############    以下向量检索的部分重写   ##################

    def _initialize_embedding_model(self) -> BaseEmbedding:
        """Initialize the embedding model"""
        return ModelScopeEmbedding(
            model_name=self.config.get("embedding_model", "iic/nlp_gte_sentence-embedding_chinese-large"),
            model_revision="master",
        )
        
    def _get_vectore_store_index(self, collection):
        try:
            # vector_store = ChromaVectorStore(
            #     chroma_collection=collection
            # )

            vector_store = ChromaVectorStore.from_collection(collection)


            return VectorStoreIndex.from_vector_store(vector_store, embed_model=self.embedding_function)
        
            # storage_context = StorageContext.from_defaults(vector_store=vector_store,
            #                                                persist_dir=self.config.get("path"))
            # return load_index_from_storage(storage_context)
        except Exception as e:
            raise Exception(f"Failed to initialize ChromaDB: {str(e)}")
        

    def _retrieve(self, question: str, collection) -> str:
        """
        Retrieves relevant documents from the vector store based on the given question.
        
        Args:
            question (str): The query string to search for.
            collection: The vector store collection to search in.
        
        Returns:
            str: Formatted results of the retrieved documents.
        
        Raises:
            Exception: If an error occurs during the retrieval process.
        
        Notes:
            - Uses hybrid vector store query mode by default.
            - Supports optional reranking and score threshold filtering.
            - BM25Retriever is commented out as it doesn't support filtering in this context.
        """
        """Retrieve relevant documents from vector store"""
        try:

            index = self._get_vectore_store_index(collection)

            retriever = VectorIndexRetriever(
                index,
                similarity_top_k=self.config.get("similarity_top_k", 10),
                vector_store_query_mode=VectorStoreQueryMode.HYBRID
            )

            ### BM25Retriever 不支持filter，因此在这个场景下无法使用
            # retriever = BM25Retriever.from_defaults(
            #     self.index, 
            #     similarity_top_k=self.similarity_top_k  # 返回 Top K 结果
            # )

            nodes = retriever.retrieve(question)

            
            # If reranker is configured, rerank the results
            if self.rerank and nodes:
                nodes = self._rerank_nodes(question, nodes)

            # Filter results based on score threshold
            if self.score_threshold and len(nodes) > 0:
                nodes = [r for r in nodes if r.score and r.score > self.score_threshold]
            
            return self._format_results(nodes)
            
        except Exception as e:
            raise Exception(f"Error in vector store retrieval: {str(e)}")

    def _rerank_nodes(self, question: str, nodes: List[NodeWithScore]) -> List[NodeWithScore]:
        """Rerank search results based on relevance to the given question.
        
        Args:
            question: The query string used for reranking.
            nodes: List of NodeWithScore objects to be reranked.
    
        Returns:
            List of reranked nodes ordered by relevance.
        """
        reranked_nodes = self.rerank.postprocess_nodes(nodes, query_bundle=QueryBundle(query_str=question))
        return reranked_nodes
        

    def get_related_ddl(self, question, **kwargs):
        return self._retrieve(question, self.ddl_collection)
    
    def get_related_documentation(self, question, **kwargs):
        return self._retrieve(question, self.documentation_collection)
    
    def get_similar_question_sql(self, question, **kwargs):
        return super().get_similar_question_sql(question, **kwargs)