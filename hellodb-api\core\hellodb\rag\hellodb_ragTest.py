import unittest
import os
import shutil
from core.hellodb.rag.hellodb_rag import HellodbRAG  # 修改导入为HellodbRAG

class HellodbRagTest(unittest.TestCase):
    def setUp(self):
        """Set up test environment before each test"""
        # Create a temporary directory for test data
        self.test_persist_dir = "test_storage"
        self.config = {
            "persist_directory": self.test_persist_dir,
            "collection_name": "test_collection"
        }
        
        # Remove any existing test directory
        if os.path.exists(self.test_persist_dir):
            shutil.rmtree(self.test_persist_dir)
            
        # 修改为使用HellodbRAG而不是ChromaDBRAG
        self.rag = HellodbRAG(config=self.config)

    # def tearDown(self):
    #     """Clean up test environment after each test"""
    #     # Remove the test storage directory
    #     if os.path.exists(self.test_persist_dir):
    #         shutil.rmtree(self.test_persist_dir)

    def test_add_and_retrieve_documents(self):
        """Test adding documents and retrieving them"""
        # Test documents
        documents = [
            "Python is a high-level programming language.",
            "ChromaDB is a vector database for AI applications.",
            "Unit testing is important for software quality.",
            "我们都有一个家，名字叫中国，兄弟姐妹都很多，景色也不错",
            "只有国家强大了，人民才有可能过上幸福的生活",
            "Python的强大支持在于大量的机器学习库和数据处理相关库",
            "Python语言真的很精彩，值得好好学习",
            "他用英语说，他喜欢python而不是Java",
            "作为一个中国人，我同时会说中文和英文两门语言",
            "作为一名程序员，我喜欢coding，当然，我喜欢和小伙伴一起coding",
            "小明是一个语言学家，他擅长法语、德语、日语等，当然中文和英语他更熟练",
            "Python是一种高级编程语言，它支持机器学习、数据处理等",
            "安哥拉蟒（学名：Python anchietae）是蟒科、蟒属的一种无毒蟒蛇"
        ]
        
        # Test metadata
        metadatas = [
            {"source": "python_doc", "type": "programming"},
            {"source": "chromadb_doc", "type": "database"},
            {"source": "testing_doc", "type": "testing"},
            {"source": "chinese_doc", "type": "chinese"},
            {"source": "chinese_doc", "type": "chinese"},
            {"source": "chinese_doc", "type": "chinese"},
            {"source": "python_doc", "type": "programming"},
            {"source": "python_doc", "type": "programming"},
            {"source": "chinese_doc", "type": "chinese"},
            {"source": "coding_doc", "type": "programming"},
            {"source": "chinese_doc", "type": "chinese"},
            {"source": "python_doc", "type": "programming"},
            {"source": "animal_doc", "type": "animal"}
        ]
        
        # Add documents
        self.rag.add_documentsions(documents)
        
        # Test retrieval with programming-related query
        query = "What programming language is mentioned?"
        results = self.rag.get_related_documentation(query)
        
        # Verify results
        self.assertIn("Python", results)
        self.assertIn("programming", results)
        self.assertIn("python_doc", results)
        
        # Test retrieval with database-related query
        query = "Tell me about vector database"
        results = self.rag.get_related_documentation(query)
        
        # Verify results
        self.assertIn("ChromaDB", results)
        self.assertIn("vector database", results)
        self.assertIn("chromadb_doc", results)
        # Verify results
        self.assertIsNotNone(results)
        # 由于HellodbRAG可能返回不同结构的结果，这里只检查结果不为空
        # 具体断言需要根据HellodbRAG的实现调整
        
        # Test retrieval with database-related query
        query = "Tell me about vector database"
        results = self.rag.get_related_documentation(query)
        
        # Verify results
        self.assertIsNotNone(results)
        # 同上，具体断言需要根据实现调整

    def test_response_language_with_specific_language(self):
        """Test that language is properly set when specified in config"""
        test_language = "Spanish"
        config = {
            "language": test_language,
            "persist_dir": self.test_persist_dir
        }
        rag = HellodbRAG(config=config)
        self.assertEqual(rag.language, test_language)
    def test_response_language_with_specific_language(self):
        """Test that language is properly set when specified in config"""
        test_language = "Chinese"
        config = {
            "language": test_language,
            "persist_directory": self.test_persist_dir
        }
        rag = HellodbRAG(config=config)
        self.assertEqual(rag.language, test_language)
        
    def test_default_language_when_not_specified(self):
        """Test that language is None when not specified in config"""
        rag = HellodbRAG(config={
            "persist_directory": self.test_persist_dir
        })
        self.assertIsNone(rag.language)

if __name__ == '__main__':
    def test_default_language_when_not_specified(self):
        """Test that language is None when not specified in config"""
        rag = HellodbRAG(config={
            "persist_directory": self.test_persist_dir
        })
        self.assertIsNone(rag.language)

    def test_rerank_functionality(self):
        """Test if reranking functionality works"""
        # 这个测试需要更具体的实现细节
        # 假设HellodbRAG有某种方式可以测试rerank
        pass

if __name__ == '__main__':
    unittest.main()