import datetime
from core.vanna.exception import HellodbTrainDataEx<PERSON>
from core.vanna.hellodb_vanna import HellodbVanna
from celery import shared_task  # 使用 
from models.datasource import DataSourceApp
from services.datasource_service import DatasourceService

from core.vanna.hellodb_vanna_factory import HellodbVannaFactory
from config import Config
from openai import OpenAI
import logging

def get_vanna_client(datasourceApp: DataSourceApp = None) -> HellodbVanna:
    config = Config()
    VANNA_LLM_MODEL = config.VANNA_LLM_MODEL
    VANNA_LLM_API_KEY = config.VANNA_LLM_API_KEY
    VANNA_LLM_BASE_URL = config.VANNA_LLM_BASE_URL

    if datasourceApp is None:
        path = '/data/chroma/default'
        initial_prompt = ''
    else:
        path = f'/data/chroma/{datasourceApp.alias}'
        initial_prompt = datasourceApp.initial_prompt

    vanna_config = {
        'path' : path,
        'model': VANNA_LLM_MODEL,
        'api_key': VANNA_LLM_API_KEY,
        'initial_prompt': initial_prompt,
        'language': '中文'
    }

    client = OpenAI(api_key=VANNA_LLM_API_KEY, base_url=VANNA_LLM_BASE_URL)

    vn = HellodbVannaFactory.create_vanna(datasourceApp, client=client, config=vanna_config)

    return vn

# 定义 Celery 任务（模块级）
@shared_task(queue='chatdb')
def train_vanna_task(app_id, datasource_id, account_id):

    try:

        if not app_id:
            raise ValueError("Invalid input parameters, you must provide app_id at least.")

        app_info = DatasourceService.get_app_by_id(app_id)
        
        if not datasource_id:
            datasource_id = app_info.datasource_id

        if not datasource_id:
            raise ValueError("Invalid input parameters, you must provide datasource_id while datasource_id is not provided in app_info.")
        
        datasource = DatasourceService.get_datasource_by_id(datasource_id)

        logging.warning(f'''
Train Task Start At {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
app_id: {app_id}
datasource_id: {datasource_id}
executor_account: {account_id}
        ''')

        vn = get_vanna_client(app_info)
        vn.connect_to_database(datasource=datasource)


        ######################  训练数据库摘要  #############################
        try:
            database_summary = vn.get_database_summary(datasource.database)
            vn.train(documentation=database_summary)
        except HellodbTrainDataException as e:
            logging.error(f"Failed to train database summary: {str(e)}")


        ######################  训练数据库结构  #############################
        df_information_schema = vn.get_information_schema(datasource.database)
        # This will break up the information schema into bite-sized chunks that can be referenced by the LLM
        plan = vn.get_training_plan_generic(df_information_schema)
        vn.train(plan=plan)
        ######################  完成训练数据库结构  ############################# 

        logging.warning(f'''
Training complete At {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
app_id: {app_id}
datasource_id: {datasource_id}
executor_account: {account_id}
''')

        return {"message": f"Training completed for app: {app_id} and datasource: {datasource_id}"}
    except Exception as e:
        logging.error(f"Training failed for app {app_id}: {str(e)}")
        raise  # 让 Celery 记录任务失败