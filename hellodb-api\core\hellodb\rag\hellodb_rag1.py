from abc import ABC, abstractmethod
import asyncio
from typing import List, Dict, Any, Optional, Union
from llama_index.core import (
    VectorStoreIndex,
    Document,
    Settings,
    StorageContext,
    load_index_from_storage,
)
from llama_index.core.node_parser import <PERSON><PERSON><PERSON><PERSON><PERSON>litter
from llama_index.core.embeddings import BaseEmbedding
from llama_index.core.vector_stores import VectorStoreQuery
from llama_index.vector_stores.chroma import ChromaVectorStore
import chromadb
from chromadb.config import Settings as ChromaSettings
import os
import numpy as np
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks
from llama_index.embeddings.modelscope import ModelScopeEmbedding
    

class HellodbRAG(ABC):
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        if config is None:
            config = {}

        self.config = config
        self.run_sql_is_set = False
        self.static_documentation = ""
        self.dialect = self.config.get("dialect", "SQL")
        self.language = self.config.get("language", None)
        self.max_tokens = self.config.get("max_tokens", 14000)
        
        # RAG specific configurations
        self.embedding_model = self.config.get("embedding_model", "iic/nlp_gte_sentence-embedding_chinese-large")
        self.reranker_model = self.config.get("reranker_model", None)
        self.collection_name = self.config.get("collection_name", "hellodb_docs")
        self.persist_dir = self.config.get("persist_directory", "./storage")
        
        # Initialize components
        self.embedding_model = self._initialize_embedding_model()
        self.node_parser = self._initialize_node_parser()
        self.vector_store = self._initialize_vector_store()
        self.index = self._initialize_index()

    @abstractmethod
    def _initialize_vector_store(self) -> Any:
        """Initialize the vector store"""
        pass

    def _initialize_embedding_model(self) -> BaseEmbedding:
        """Initialize the embedding model"""
        return ModelScopeEmbedding(
            model_name=self.embedding_model,
            model_revision="master",
        )
        

    def _initialize_node_parser(self) -> SentenceSplitter:
        """Initialize the node parser"""
        return SentenceSplitter(
            chunk_size=self.config.get("chunk_size", 512),
            chunk_overlap=self.config.get("chunk_overlap", 50)
        )

    def _initialize_index(self) -> VectorStoreIndex:
        """Initialize or load the index"""
        try:
            # Try to load existing index
            storage_context = StorageContext.from_defaults(vector_store=self.vector_store)
            return load_index_from_storage(storage_context)
        except Exception:
            # Create new index if loading fails
            return VectorStoreIndex.from_vector_store(
                vector_store=self.vector_store,
                embed_model=self.embedding_model
            )

    def _response_language(self) -> str:
        if self.language is None:
            return ""
        return f"Respond in the {self.language} language."

    def add_documents(self, documents: List[str], metadatas: Optional[List[Dict[str, Any]]] = None):
        """Add documents to the vector store"""
        if not documents:
            return

        if metadatas is None:
            metadatas = [{} for _ in documents]

        # Create Document objects
        docs = [
            Document(text=doc, metadata=metadata)
            for doc, metadata in zip(documents, metadatas)
        ]

        # Insert documents into index
        self.index.insert_nodes(self.node_parser.get_nodes_from_documents(docs))

    def retrive(self, question: str, **kwargs) -> str:
        """Retrieve relevant documents from vector store"""
        try:
            # Get number of results to return
            n_results = kwargs.get("n_results", 5)
            
            # Create query
            query = VectorStoreQuery(
                query_embedding=self.embedding_model.get_text_embedding(question),
                similarity_top_k=n_results
            )
            
            # Execute query
            results = self.vector_store.query(query)
            
            # If reranker is configured, rerank the results
            if self.reranker_model and results.nodes:
                reranked_results = self._rerank_results(question, results)
                return self._format_results(reranked_results)
            
            return self._format_results(results)
            
        except Exception as e:
            raise Exception(f"Error in vector store retrieval: {str(e)}")

    def _rerank_results(self, question: str, results: Any) -> Any:
        """Rerank results using the configured reranker model"""
        # TODO: Implement reranking logic based on the configured reranker model
        return results

    def _format_results(self, results: Any) -> str:
        """Format the results into a string"""
        if not results.nodes:
            return "No relevant documents found."
            
        formatted_results = []
        for i, node in enumerate(results.nodes):
            formatted_results.append(
                f"Document {i+1}:\n{node.text}\nMetadata: {node.metadata}\n"
            )
            
        return "\n".join(formatted_results)

    def clear_collection(self):
        """Clear all documents from the collection"""
        # This method should be implemented by subclasses
        pass

    def persist(self):
        """Persist the index to disk"""
        self.index.storage_context.persist(persist_dir=self.persist_dir)


class ChromaDBRAG(HellodbRAG):
    def _initialize_vector_store(self) -> ChromaVectorStore:
        """Initialize ChromaDB vector store"""
        try:
            # Initialize ChromaDB client
            self.chroma_client = chromadb.Client(ChromaSettings(
                persist_directory=os.path.join(self.persist_dir, "chroma"),
                anonymized_telemetry=False
            ))
            
            # Get or create collection
            collection = self.chroma_client.get_or_create_collection(
                name=self.collection_name
            )
            
            # Create ChromaVectorStore
            return ChromaVectorStore(
                chroma_collection=collection,
                embed_model=self.embedding_model
            )
            
        except Exception as e:
            raise Exception(f"Failed to initialize ChromaDB: {str(e)}")
            
    def clear_collection(self):
        """Clear all documents from the collection"""
        try:
            # Delete the collection if it exists
            self.chroma_client.delete_collection(self.collection_name)
            
                chroma_collection=collection,
                embed_model=self.embedding_model
            )
            
        except Exception as e:
            raise Exception(f"Failed to initialize ChromaDB: {str(e)}")
            
    def clear_collection(self):
        """Clear all documents from the collection"""
        try:
            # Delete the collection if it exists
            self.chroma_client.delete_collection(self.collection_name)
            
            # Recreate the collection
            collection = self.chroma_client.create_collection(
                name=self.collection_name
            )
            
            # Update the vector store and index
            self.vector_store = ChromaVectorStore(
                chroma_collection=collection,
                embed_model=self.embedding_model
            )
            self.index = self._initialize_index()
        except Exception as e:
            raise Exception(f"Failed to clear collection: {str(e)}")