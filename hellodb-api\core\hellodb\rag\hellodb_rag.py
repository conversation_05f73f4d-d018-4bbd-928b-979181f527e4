from abc import ABC, abstractmethod
import asyncio
import json
from typing import List, Dict, Any, Optional, Union
from llama_index.core import (
    VectorStoreIndex,
    Document,
    StorageContext,
    load_index_from_storage,
)
from llama_index.core.node_parser import <PERSON><PERSON><PERSON><PERSON><PERSON>litter
from llama_index.core.embeddings import BaseEmbedding
from llama_index.vector_stores.chroma import ChromaVectorStore
import chromadb
from chromadb.config import Settings
import os
import numpy as np
from llama_index.core.postprocessor.types import BaseNodePostprocessor
from llama_index.core.vector_stores.types import BasePydanticVectorStore
from llama_index.core.postprocessor import SentenceTransformerRerank
from llama_index.core.schema import QueryBundle, NodeWithScore
from llama_index.core.indices.vector_store.retrievers import VectorIndexRetriever
from llama_index.retrievers.bm25 import BM25Retriever

from llama_index.core.vector_stores.types import (
    MetadataFilter,
    MetadataFilters,
    VectorStoreQuery,
    VectorStoreQueryMode,
    VectorStoreQueryResult,
)

from llama_index.embeddings.dashscope import (
    DashScopeEmbedding,
    DashScopeTextEmbeddingModels,
    DashScopeTextEmbeddingType,
)

from llama_index.postprocessor.dashscope_rerank import DashScopeRerank

os.environ["DASHSCOPE_API_KEY"] = "sk-c4793b0c284e448ba1a611aa6f424062"

class HellodbRAG(ABC):
    def __init__(self, config: Optional[Dict[str, Any]] = None):

        os.environ["DASHSCOPE_API_KEY"] = "sk-c4793b0c284e448ba1a611aa6f424062"

        if config is None:
            config = {}

        self.config = config
        self.run_sql_is_set = False
        self.static_documentation = ""
        self.dialect = self.config.get("dialect", "SQL")
        self.language = self.config.get("language", None)
        self.max_tokens = self.config.get("max_tokens", 14000)
        self.chunk_size = self.config.get("chunk_size", 20480)
        self.chunk_overlap = self.config.get("chunk_overlap", 50)
        self.top_n = self.config.get("top_n", 5)
        self.similarity_top_k = self.config.get("similarity_top_k", 10) # 检索相似度前k个
        self.score_threshold = self.config.get("score_threshold", 0.5)

        self.base_url = self.config.get('base_url', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
        
        # RAG specific configurations
        # self.embedding_model = self.config.get("embedding_model", "iic/nlp_gte_sentence-embedding_chinese-large")
        self.api_key = self.config.get("api_key", os.environ["DASHSCOPE_API_KEY"])
        self.reranker_model = self.config.get("reranker_model", None)
        self.embedding_model = self._initialize_embedding_model()
        self.rerank = self._initialize_reranker_modal()
        self.node_parser = self._initialize_node_parser()

        self.collection_name_documentation = 'documentation'
        self.collection_name_ddl = 'ddl'
        self.collection_name_sql = 'sql'

        chromadb_dir = self.config.get("persist_dir", "/data/hellodb/chromadb")
        path = self.config.get("path", ".")
        # persist_dir为chromadb_dir 和 path的拼接
        self.persist_dir = os.path.join(chromadb_dir, path)

        curr_client = config.get("client", "persistent")
        if curr_client == "persistent":
            self.chroma_client = chromadb.PersistentClient(
                path=self.persist_dir, settings=Settings(anonymized_telemetry=False)
            )
        elif curr_client == "in-memory":
            self.chroma_client = chromadb.EphemeralClient(
                settings=Settings(anonymized_telemetry=False)
            )
        elif isinstance(curr_client, chromadb.api.client.Client):
            # allow providing client directly
            self.chroma_client = curr_client
        else:
            raise ValueError(f"Unsupported client was set in config: {curr_client}")

    def _initialize_embedding_model(self) -> BaseEmbedding:
        """Initialize the embedding model"""
        try:
            # 使用正确的模型名称
            model_name = self.config.get("embedding_model", "text-embedding-v1")
            
            embedding_model = DashScopeEmbedding(
                model_name=model_name,
                text_type=DashScopeTextEmbeddingType.TEXT_TYPE_DOCUMENT,
                api_key=self.api_key
            )
            
            # 测试embedding模型是否正常工作
            test_text = "测试文本"
            test_embedding = embedding_model.get_text_embedding(test_text)
            if test_embedding is None:
                raise ValueError("Embedding model returned None - check API key and model name")
            
            print(f"Successfully initialized embedding model: {model_name}")
            return embedding_model
            
        except Exception as e:
            print(f"Failed to initialize embedding model: {e}")
            raise
    
    def _initialize_reranker_modal(self) -> BaseNodePostprocessor:
        """Initialize the reranker model"""
        return DashScopeRerank(
            model=self.config.get("reranker_model", "gte-rerank"),
            top_n=self.top_n,
            api_key=self.api_key
        )

    def _initialize_node_parser(self) -> SentenceSplitter:
        """Initialize the node parser"""
        return SentenceSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap
        )

    def _get_vectore_store_index(self, collection: str):
        try:
            chroma_collection = self.chroma_client.get_or_create_collection("collection")
            # Set up the ChromaVectorStore and StorageContext
            vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
            return VectorStoreIndex.from_vector_store(vector_store, embed_model=self.embedding_model)
        except Exception as e:
            raise Exception(f"Failed to initialize ChromaDB: {str(e)}")

    def _add_document(self, document: str, metadata: Optional[Dict[str, Any]] = None, collection: str = None):
        """Add a single document to the vector store"""
        if not document:
            return
        
        ## FIXME 这里还报错
        # Create Document object
        doc = Document(text=document, metadata=metadata)
        index = self._get_vectore_store_index(collection)
        # Insert document into index
        
        index.insert_nodes(self.node_parser.get_nodes_from_documents([doc]))

    
    def _add_documents(self, documents: List[str], metadatas: Optional[List[Dict[str, Any]]] = None, collection: str = None):
        """Add documents to the vector store"""
        if not documents:
            return

        if metadatas is None:
            metadatas = [{} for _ in documents]

        # Create Document objects
        docs = [
            Document(text=doc, metadata=metadata)
            for doc, metadata in zip(documents, metadatas)
        ]
        
        index = self._get_vectore_store_index(collection)

        # Insert documents into index
        index.insert_nodes(self.node_parser.get_nodes_from_documents(docs))

    def _retrieve(self, question: str, collection) -> str:
        """
        Retrieves relevant documents from the vector store based on the given question.
        
        Args:
            question (str): The query string to search for.
            collection: The vector store collection to search in.
        
        Returns:
            str: Formatted results of the retrieved documents.
        
        Raises:
            Exception: If an error occurs during the retrieval process.
        
        Notes:
            - Uses hybrid vector store query mode by default.
            - Supports optional reranking and score threshold filtering.

            - BM25Retriever is commented out as it doesn't support filtering in this context.
        """
        try:

            index = self._get_vectore_store_index(collection)

            retriever = VectorIndexRetriever(
                index,
                similarity_top_k=self.config.get("similarity_top_k", 10),
                vector_store_query_mode=VectorStoreQueryMode.HYBRID
            )

            ### BM25Retriever 不支持filter，因此在这个场景下无法使用
            # retriever = BM25Retriever.from_defaults(
            #     self.index, 
            #     similarity_top_k=self.similarity_top_k  # 返回 Top K 结果
            # )

            nodes = retriever.retrieve(question)

            
            # If reranker is configured, rerank the results
            if self.rerank and nodes:
                nodes = self._rerank_nodes(question, nodes)

            # Filter results based on score threshold
            if self.score_threshold and len(nodes) > 0:
                nodes = [r for r in nodes if r.score and r.score > self.score_threshold]
            
            return self._format_results(nodes)
            
        except Exception as e:
            raise Exception(f"Error in vector store retrieval: {str(e)}")

    def _rerank_nodes(self, question: str, nodes: List[NodeWithScore]) -> List[NodeWithScore]:
        """Rerank search results based on relevance to the given question.
        
        Args:
            question: The query string used for reranking.
            nodes: List of NodeWithScore objects to be reranked.
    
        Returns:
            List of reranked nodes ordered by relevance.
        """
        reranked_nodes = self.rerank.postprocess_nodes(nodes, query_bundle=QueryBundle(query_str=question))
        return reranked_nodes

    def _format_results(self, nodes: Any) -> str:
        """Format the results into a string"""
        if not nodes:
            return "No relevant documents found."
            
        formatted_results = []
        for i, node in enumerate(nodes):
            formatted_results.append(
                f"Document {i+1}:\n{node.text}\nMetadata: {node.metadata}\n"
            )

        print(formatted_results)
            
        return "\n".join(formatted_results)


    def persist(self):
        """Persist the index to disk"""
        self.index.storage_context.persist(persist_dir=self.persist_dir)

    
    def get_related_ddl(self, question, **kwargs):
        return self._retrieve(question, self.collection_name_ddl)
    
    def get_related_documentation(self, question, **kwargs):
        return self._retrieve(question, self.collection_name_documentation)
    
    def get_similar_question_sql(self, question, **kwargs):
        return self._retrieve(question, self.collection_name_sql)
    

    def add_question_sql(self, question: str, sql: str, **kwargs) -> str:
        question_sql_json = json.dumps(
            {
                "question": question,
                "sql": sql,
            },
            ensure_ascii=False,
        )
        return self._add_document(question_sql_json, None, self.collection_name_sql)
    
    def add_ddl(self, ddl: str, **kwargs) -> str:
        return self._add_document(ddl, None, self.collection_name_ddl)

    def add_documentation(self, documentation: str, **kwargs) -> str:
        return self._add_document(documentation, None, self.collection_name_documentation)
    
    def add_documentsions(self, documentations: List[str], **kwargs) -> str:
        return self._add_documents(documentations, None, self.collection_name_documentation)
