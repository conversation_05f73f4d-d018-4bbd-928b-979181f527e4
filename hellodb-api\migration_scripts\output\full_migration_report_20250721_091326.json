{"full_migration_summary": {"total_apps": 45, "migrated_apps": 3, "created_apps": 0, "skipped_apps": 0, "error_apps": 42, "start_time": 1753060367.1344957, "results": [{"app_alias": "Stop_lm", "action": "migrated", "success": false, "result": null}, {"app_alias": "ad-data", "action": "migrated", "success": false, "result": null}, {"app_alias": "ali<PERSON>-wl", "action": "migrated", "success": false, "result": null}, {"app_alias": "asda", "action": "migrated", "success": false, "result": null}, {"app_alias": "assets", "action": "migrated", "success": false, "result": null}, {"app_alias": "barcode", "action": "migrated", "success": false, "result": null}, {"app_alias": "bigdata_new", "action": "migrated", "success": false, "result": null}, {"app_alias": "cangchu", "action": "migrated", "success": false, "result": null}, {"app_alias": "chery", "action": "migrated", "success": false, "result": null}, {"app_alias": "da-data", "action": "migrated", "success": false, "result": null}, {"app_alias": "ddx", "action": "migrated", "success": false, "result": null}, {"app_alias": "demo", "action": "migrated", "success": false, "result": null}, {"app_alias": "difydb_assistant", "action": "migrated", "success": false, "result": null}, {"app_alias": "dxrtest", "action": "migrated", "success": false, "result": null}, {"app_alias": "ecology", "action": "migrated", "success": false, "result": null}, {"app_alias": "edu", "action": "migrated", "success": false, "result": null}, {"app_alias": "haishen", "action": "migrated", "success": false, "result": null}, {"app_alias": "hh", "action": "migrated", "success": false, "result": null}, {"app_alias": "hk", "action": "migrated", "success": false, "result": null}, {"app_alias": "hrzl", "action": "migrated", "success": false, "result": null}, {"app_alias": "icms", "action": "migrated", "success": false, "result": null}, {"app_alias": "jw", "action": "migrated", "success": false, "result": null}, {"app_alias": "ky", "action": "migrated", "success": false, "result": null}, {"app_alias": "library collections finder", "action": "migrated", "success": false, "result": null}, {"app_alias": "pop", "action": "migrated", "success": false, "result": null}, {"app_alias": "postgres_dev", "action": "migrated", "success": false, "result": null}, {"app_alias": "score", "action": "migrated", "success": false, "result": null}, {"app_alias": "sensechat_prompt", "action": "migrated", "success": false, "result": null}, {"app_alias": "server-mysql-app", "action": "migrated", "success": false, "result": null}, {"app_alias": "test", "action": "migrated", "success": true, "result": {"app_alias": "test", "migration_stats": {"sql": 0, "ddl": 0, "documentation": 17, "errors": []}, "verification_result": {"success": true, "details": {"sql": {"old_count": 0, "new_count": 0, "match": true}, "ddl": {"old_count": 0, "new_count": 0, "match": true}, "documentation": {"old_count": 17, "new_count": 17, "match": true}}}, "success": true}}, {"app_alias": "test2", "action": "migrated", "success": false, "result": null}, {"app_alias": "test234", "action": "migrated", "success": false, "result": null}, {"app_alias": "testdb", "action": "migrated", "success": true, "result": {"app_alias": "testdb", "migration_stats": {"sql": 0, "ddl": 0, "documentation": 16, "errors": []}, "verification_result": {"success": true, "details": {"sql": {"old_count": 0, "new_count": 0, "match": true}, "ddl": {"old_count": 0, "new_count": 0, "match": true}, "documentation": {"old_count": 16, "new_count": 16, "match": true}}}, "success": true}}, {"app_alias": "<PERSON><PERSON><PERSON><PERSON>", "action": "migrated", "success": false, "result": null}, {"app_alias": "testmysql", "action": "migrated", "success": false, "result": null}, {"app_alias": "testnew", "action": "migrated", "success": false, "result": null}, {"app_alias": "testnew2", "action": "migrated", "success": true, "result": {"app_alias": "testnew2", "migration_stats": {"sql": 0, "ddl": 0, "documentation": 17, "errors": []}, "verification_result": {"success": true, "details": {"sql": {"old_count": 0, "new_count": 0, "match": true}, "ddl": {"old_count": 0, "new_count": 0, "match": true}, "documentation": {"old_count": 17, "new_count": 17, "match": true}}}, "success": true}}, {"app_alias": "testnew3", "action": "migrated", "success": false, "result": null}, {"app_alias": "text_DBselect", "action": "migrated", "success": false, "result": null}, {"app_alias": "train_query", "action": "migrated", "success": false, "result": null}, {"app_alias": "yb", "action": "migrated", "success": false, "result": null}, {"app_alias": "yjk", "action": "migrated", "success": false, "result": null}, {"app_alias": "ysrdnj", "action": "migrated", "success": false, "result": null}, {"app_alias": "yy", "action": "migrated", "success": false, "result": null}, {"app_alias": "zhxy", "action": "migrated", "success": false, "result": null}], "elapsed_time": 38.86644124984741}, "generated_at": "2025-07-21 09:13:26"}