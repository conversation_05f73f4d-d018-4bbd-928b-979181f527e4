{"verification_summary": {"total_apps": 8, "success_count": 8, "error_count": 0, "results": [{"app_alias": "test", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 17, "total_count": 17}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 5, "response_time": 0.5617384910583496, "has_results": true}, "查询": {"result_count": 4, "response_time": 0.5551245212554932, "has_results": true}, "表结构": {"result_count": 5, "response_time": 0.5175418853759766, "has_results": true}, "用户信息": {"result_count": 4, "response_time": 0.5895471572875977, "has_results": true}}, "overall_success": true}, {"app_alias": "testdb", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 16, "total_count": 16}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 5, "response_time": 0.5966482162475586, "has_results": true}, "查询": {"result_count": 3, "response_time": 0.5658140182495117, "has_results": true}, "表结构": {"result_count": 5, "response_time": 0.5525665283203125, "has_results": true}, "用户信息": {"result_count": 3, "response_time": 0.5120978355407715, "has_results": true}}, "overall_success": true}, {"app_alias": "testnew2", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 17, "total_count": 17}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 5, "response_time": 0.5482258796691895, "has_results": true}, "查询": {"result_count": 4, "response_time": 0.5132465362548828, "has_results": true}, "表结构": {"result_count": 5, "response_time": 0.5361602306365967, "has_results": true}, "用户信息": {"result_count": 4, "response_time": 0.6030428409576416, "has_results": true}}, "overall_success": true}, {"app_alias": "Stop_lm", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 0, "total_count": 0}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 0, "response_time": 0.24987196922302246, "has_results": false}, "查询": {"result_count": 0, "response_time": 0.2382187843322754, "has_results": false}, "表结构": {"result_count": 0, "response_time": 0.2349708080291748, "has_results": false}, "用户信息": {"result_count": 0, "response_time": 0.21355676651000977, "has_results": false}}, "overall_success": true}, {"app_alias": "ad-data", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 0, "total_count": 0}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 0, "response_time": 0.24284029006958008, "has_results": false}, "查询": {"result_count": 0, "response_time": 0.2126293182373047, "has_results": false}, "表结构": {"result_count": 0, "response_time": 0.20116448402404785, "has_results": false}, "用户信息": {"result_count": 0, "response_time": 0.2224588394165039, "has_results": false}}, "overall_success": true}, {"app_alias": "ali<PERSON>-wl", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 0, "total_count": 0}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 0, "response_time": 0.24190092086791992, "has_results": false}, "查询": {"result_count": 0, "response_time": 0.23463892936706543, "has_results": false}, "表结构": {"result_count": 0, "response_time": 0.2409524917602539, "has_results": false}, "用户信息": {"result_count": 0, "response_time": 0.24102783203125, "has_results": false}}, "overall_success": true}, {"app_alias": "asda", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 0, "total_count": 0}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 0, "response_time": 0.2285480499267578, "has_results": false}, "查询": {"result_count": 0, "response_time": 0.23047685623168945, "has_results": false}, "表结构": {"result_count": 0, "response_time": 0.21347761154174805, "has_results": false}, "用户信息": {"result_count": 0, "response_time": 0.20451092720031738, "has_results": false}}, "overall_success": true}, {"app_alias": "assets", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 0, "total_count": 0}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 0, "response_time": 0.2395782470703125, "has_results": false}, "查询": {"result_count": 0, "response_time": 0.20747780799865723, "has_results": false}, "表结构": {"result_count": 0, "response_time": 0.2336106300354004, "has_results": false}, "用户信息": {"result_count": 0, "response_time": 0.20791840553283691, "has_results": false}}, "overall_success": true}]}, "verification_log": [{"timestamp": "2025-07-21 09:44:41", "level": "INFO", "message": "开始验证 8 个迁移的应用"}, {"timestamp": "2025-07-21 09:44:41", "level": "INFO", "message": "开始验证应用: test"}, {"timestamp": "2025-07-21 09:44:41", "level": "INFO", "message": "验证应用 test 的数据数量..."}, {"timestamp": "2025-07-21 09:44:41", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:41", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:41", "level": "INFO", "message": "  Documentation collection: 17 个文档"}, {"timestamp": "2025-07-21 09:44:41", "level": "INFO", "message": "测试应用 test 的检索功能..."}, {"timestamp": "2025-07-21 09:44:42", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-21 09:44:42", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:42", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:42", "level": "INFO", "message": "测试应用 test 的embedding质量..."}, {"timestamp": "2025-07-21 09:44:43", "level": "INFO", "message": "  查询 '数据库': 5 个结果, 响应时间: 0.562秒"}, {"timestamp": "2025-07-21 09:44:43", "level": "INFO", "message": "  查询 '查询': 4 个结果, 响应时间: 0.555秒"}, {"timestamp": "2025-07-21 09:44:44", "level": "INFO", "message": "  查询 '表结构': 5 个结果, 响应时间: 0.518秒"}, {"timestamp": "2025-07-21 09:44:44", "level": "INFO", "message": "  查询 '用户信息': 4 个结果, 响应时间: 0.590秒"}, {"timestamp": "2025-07-21 09:44:44", "level": "SUCCESS", "message": "应用 test 验证成功"}, {"timestamp": "2025-07-21 09:44:44", "level": "INFO", "message": "开始验证应用: testdb"}, {"timestamp": "2025-07-21 09:44:44", "level": "INFO", "message": "验证应用 testdb 的数据数量..."}, {"timestamp": "2025-07-21 09:44:45", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:45", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:45", "level": "INFO", "message": "  Documentation collection: 16 个文档"}, {"timestamp": "2025-07-21 09:44:45", "level": "INFO", "message": "测试应用 testdb 的检索功能..."}, {"timestamp": "2025-07-21 09:44:45", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-21 09:44:46", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:46", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:46", "level": "INFO", "message": "测试应用 testdb 的embedding质量..."}, {"timestamp": "2025-07-21 09:44:46", "level": "INFO", "message": "  查询 '数据库': 5 个结果, 响应时间: 0.597秒"}, {"timestamp": "2025-07-21 09:44:47", "level": "INFO", "message": "  查询 '查询': 3 个结果, 响应时间: 0.566秒"}, {"timestamp": "2025-07-21 09:44:48", "level": "INFO", "message": "  查询 '表结构': 5 个结果, 响应时间: 0.553秒"}, {"timestamp": "2025-07-21 09:44:48", "level": "INFO", "message": "  查询 '用户信息': 3 个结果, 响应时间: 0.512秒"}, {"timestamp": "2025-07-21 09:44:48", "level": "SUCCESS", "message": "应用 testdb 验证成功"}, {"timestamp": "2025-07-21 09:44:48", "level": "INFO", "message": "开始验证应用: testnew2"}, {"timestamp": "2025-07-21 09:44:48", "level": "INFO", "message": "验证应用 testnew2 的数据数量..."}, {"timestamp": "2025-07-21 09:44:48", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:48", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:48", "level": "INFO", "message": "  Documentation collection: 17 个文档"}, {"timestamp": "2025-07-21 09:44:48", "level": "INFO", "message": "测试应用 testnew2 的检索功能..."}, {"timestamp": "2025-07-21 09:44:49", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-21 09:44:49", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:49", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:49", "level": "INFO", "message": "测试应用 testnew2 的embedding质量..."}, {"timestamp": "2025-07-21 09:44:50", "level": "INFO", "message": "  查询 '数据库': 5 个结果, 响应时间: 0.548秒"}, {"timestamp": "2025-07-21 09:44:50", "level": "INFO", "message": "  查询 '查询': 4 个结果, 响应时间: 0.513秒"}, {"timestamp": "2025-07-21 09:44:51", "level": "INFO", "message": "  查询 '表结构': 5 个结果, 响应时间: 0.536秒"}, {"timestamp": "2025-07-21 09:44:51", "level": "INFO", "message": "  查询 '用户信息': 4 个结果, 响应时间: 0.603秒"}, {"timestamp": "2025-07-21 09:44:51", "level": "SUCCESS", "message": "应用 testnew2 验证成功"}, {"timestamp": "2025-07-21 09:44:51", "level": "INFO", "message": "开始验证应用: Stop_lm"}, {"timestamp": "2025-07-21 09:44:51", "level": "INFO", "message": "验证应用 Stop_lm 的数据数量..."}, {"timestamp": "2025-07-21 09:44:52", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:52", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:52", "level": "INFO", "message": "  Documentation collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:52", "level": "INFO", "message": "测试应用 Stop_lm 的检索功能..."}, {"timestamp": "2025-07-21 09:44:52", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-21 09:44:52", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:52", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:52", "level": "INFO", "message": "测试应用 Stop_lm 的embedding质量..."}, {"timestamp": "2025-07-21 09:44:53", "level": "INFO", "message": "  查询 '数据库': 0 个结果, 响应时间: 0.250秒"}, {"timestamp": "2025-07-21 09:44:53", "level": "INFO", "message": "  查询 '查询': 0 个结果, 响应时间: 0.238秒"}, {"timestamp": "2025-07-21 09:44:53", "level": "INFO", "message": "  查询 '表结构': 0 个结果, 响应时间: 0.235秒"}, {"timestamp": "2025-07-21 09:44:53", "level": "INFO", "message": "  查询 '用户信息': 0 个结果, 响应时间: 0.214秒"}, {"timestamp": "2025-07-21 09:44:53", "level": "SUCCESS", "message": "应用 Stop_lm 验证成功"}, {"timestamp": "2025-07-21 09:44:53", "level": "INFO", "message": "开始验证应用: ad-data"}, {"timestamp": "2025-07-21 09:44:53", "level": "INFO", "message": "验证应用 ad-data 的数据数量..."}, {"timestamp": "2025-07-21 09:44:53", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:53", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:53", "level": "INFO", "message": "  Documentation collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:53", "level": "INFO", "message": "测试应用 ad-data 的检索功能..."}, {"timestamp": "2025-07-21 09:44:54", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-21 09:44:54", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:54", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:54", "level": "INFO", "message": "测试应用 ad-data 的embedding质量..."}, {"timestamp": "2025-07-21 09:44:54", "level": "INFO", "message": "  查询 '数据库': 0 个结果, 响应时间: 0.243秒"}, {"timestamp": "2025-07-21 09:44:55", "level": "INFO", "message": "  查询 '查询': 0 个结果, 响应时间: 0.213秒"}, {"timestamp": "2025-07-21 09:44:55", "level": "INFO", "message": "  查询 '表结构': 0 个结果, 响应时间: 0.201秒"}, {"timestamp": "2025-07-21 09:44:55", "level": "INFO", "message": "  查询 '用户信息': 0 个结果, 响应时间: 0.222秒"}, {"timestamp": "2025-07-21 09:44:55", "level": "SUCCESS", "message": "应用 ad-data 验证成功"}, {"timestamp": "2025-07-21 09:44:55", "level": "INFO", "message": "开始验证应用: aliyun-wl"}, {"timestamp": "2025-07-21 09:44:55", "level": "INFO", "message": "验证应用 aliyun-wl 的数据数量..."}, {"timestamp": "2025-07-21 09:44:55", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:55", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:55", "level": "INFO", "message": "  Documentation collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:55", "level": "INFO", "message": "测试应用 aliyun-wl 的检索功能..."}, {"timestamp": "2025-07-21 09:44:55", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-21 09:44:56", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:56", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:56", "level": "INFO", "message": "测试应用 aliyun-wl 的embedding质量..."}, {"timestamp": "2025-07-21 09:44:56", "level": "INFO", "message": "  查询 '数据库': 0 个结果, 响应时间: 0.242秒"}, {"timestamp": "2025-07-21 09:44:56", "level": "INFO", "message": "  查询 '查询': 0 个结果, 响应时间: 0.235秒"}, {"timestamp": "2025-07-21 09:44:57", "level": "INFO", "message": "  查询 '表结构': 0 个结果, 响应时间: 0.241秒"}, {"timestamp": "2025-07-21 09:44:57", "level": "INFO", "message": "  查询 '用户信息': 0 个结果, 响应时间: 0.241秒"}, {"timestamp": "2025-07-21 09:44:57", "level": "SUCCESS", "message": "应用 aliyun-wl 验证成功"}, {"timestamp": "2025-07-21 09:44:57", "level": "INFO", "message": "开始验证应用: asda"}, {"timestamp": "2025-07-21 09:44:57", "level": "INFO", "message": "验证应用 asda 的数据数量..."}, {"timestamp": "2025-07-21 09:44:57", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:57", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:57", "level": "INFO", "message": "  Documentation collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:57", "level": "INFO", "message": "测试应用 asda 的检索功能..."}, {"timestamp": "2025-07-21 09:44:57", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-21 09:44:57", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:58", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:58", "level": "INFO", "message": "测试应用 asda 的embedding质量..."}, {"timestamp": "2025-07-21 09:44:58", "level": "INFO", "message": "  查询 '数据库': 0 个结果, 响应时间: 0.229秒"}, {"timestamp": "2025-07-21 09:44:58", "level": "INFO", "message": "  查询 '查询': 0 个结果, 响应时间: 0.230秒"}, {"timestamp": "2025-07-21 09:44:58", "level": "INFO", "message": "  查询 '表结构': 0 个结果, 响应时间: 0.213秒"}, {"timestamp": "2025-07-21 09:44:59", "level": "INFO", "message": "  查询 '用户信息': 0 个结果, 响应时间: 0.205秒"}, {"timestamp": "2025-07-21 09:44:59", "level": "SUCCESS", "message": "应用 asda 验证成功"}, {"timestamp": "2025-07-21 09:44:59", "level": "INFO", "message": "开始验证应用: assets"}, {"timestamp": "2025-07-21 09:44:59", "level": "INFO", "message": "验证应用 assets 的数据数量..."}, {"timestamp": "2025-07-21 09:44:59", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:59", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:59", "level": "INFO", "message": "  Documentation collection: 0 个文档"}, {"timestamp": "2025-07-21 09:44:59", "level": "INFO", "message": "测试应用 assets 的检索功能..."}, {"timestamp": "2025-07-21 09:44:59", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-21 09:44:59", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:59", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-21 09:44:59", "level": "INFO", "message": "测试应用 assets 的embedding质量..."}, {"timestamp": "2025-07-21 09:45:00", "level": "INFO", "message": "  查询 '数据库': 0 个结果, 响应时间: 0.240秒"}, {"timestamp": "2025-07-21 09:45:00", "level": "INFO", "message": "  查询 '查询': 0 个结果, 响应时间: 0.207秒"}, {"timestamp": "2025-07-21 09:45:00", "level": "INFO", "message": "  查询 '表结构': 0 个结果, 响应时间: 0.234秒"}, {"timestamp": "2025-07-21 09:45:00", "level": "INFO", "message": "  查询 '用户信息': 0 个结果, 响应时间: 0.208秒"}, {"timestamp": "2025-07-21 09:45:00", "level": "SUCCESS", "message": "应用 assets 验证成功"}, {"timestamp": "2025-07-21 09:45:00", "level": "INFO", "message": "验证完成: 成功=8, 失败=0"}], "generated_at": "2025-07-21 09:45:00"}