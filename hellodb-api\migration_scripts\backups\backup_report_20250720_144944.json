{"backup_summary": {"backup_time": "2025-07-20 14:49:41", "total_apps": 45, "success_count": 45, "error_count": 0, "manifests": [{"app_alias": "ad-data", "backup_time": "20250720_144941", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144941/ad-data/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144941/ad-data/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "ali<PERSON>-wl", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/aliyun-wl/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/aliyun-wl/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "asda", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/asda/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/asda/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "assets", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/assets/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/assets/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "barcode", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/barcode/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/barcode/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "bigdata_new", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/bigdata_new/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/bigdata_new/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "cangchu", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/cangchu/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/cangchu/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "chery", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/chery/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/chery/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "da-data", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/da-data/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/da-data/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "ddx", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/ddx/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/ddx/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "demo", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/demo/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/demo/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "difydb_assistant", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/difydb_assistant/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/difydb_assistant/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "dxrtest", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/dxrtest/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/dxrtest/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "ecology", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/ecology/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/ecology/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "edu", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/edu/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/edu/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "haishen", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/haishen/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/haishen/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "hh", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/hh/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/hh/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "hk", "backup_time": "20250720_144942", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144942/hk/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144942/hk/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "hrzl", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/hrzl/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/hrzl/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "icms", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/icms/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/icms/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "jw", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/jw/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/jw/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "ky", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/ky/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/ky/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "library collections finder", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/library collections finder/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/library collections finder/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "pop", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/pop/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/pop/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "postgres_dev", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/postgres_dev/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/postgres_dev/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "score", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/score/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/score/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "sensechat_prompt", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/sensechat_prompt/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/sensechat_prompt/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "server-mysql-app", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/server-mysql-app/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/server-mysql-app/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "Stop_lm", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/Stop_lm/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/Stop_lm/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "test", "backup_time": "20250720_144943", "total_documents": 17, "backup_file": "migration_scripts/backups/20250720_144943/test/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/test/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 17}}, {"app_alias": "test2", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/test2/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/test2/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "test234", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/test234/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/test234/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "testdb", "backup_time": "20250720_144943", "total_documents": 16, "backup_file": "migration_scripts/backups/20250720_144943/testdb/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/testdb/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 16}}, {"app_alias": "<PERSON><PERSON><PERSON><PERSON>", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/testjerrylu/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/testjerrylu/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "testmysql", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/testmysql/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/testmysql/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "testnew", "backup_time": "20250720_144943", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144943/testnew/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144943/testnew/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "testnew2", "backup_time": "20250720_144944", "total_documents": 17, "backup_file": "migration_scripts/backups/20250720_144944/testnew2/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144944/testnew2/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 17}}, {"app_alias": "testnew3", "backup_time": "20250720_144944", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144944/testnew3/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144944/testnew3/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "text_DBselect", "backup_time": "20250720_144944", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144944/text_DBselect/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144944/text_DBselect/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "train_query", "backup_time": "20250720_144944", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144944/train_query/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144944/train_query/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "yb", "backup_time": "20250720_144944", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144944/yb/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144944/yb/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "yjk", "backup_time": "20250720_144944", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144944/yjk/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144944/yjk/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "ysrdnj", "backup_time": "20250720_144944", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144944/ysrdnj/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144944/ysrdnj/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "yy", "backup_time": "20250720_144944", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144944/yy/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144944/yy/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}, {"app_alias": "zhxy", "backup_time": "20250720_144944", "total_documents": 0, "backup_file": "migration_scripts/backups/20250720_144944/zhxy/backup_data.json", "chroma_backup_dir": "migration_scripts/backups/20250720_144944/zhxy/chroma_files", "collections_summary": {"sql": 0, "ddl": 0, "documentation": 0}}]}, "backup_log": [{"timestamp": "2025-07-20 14:49:41", "level": "INFO", "message": "开始备份 45 个应用"}, {"timestamp": "2025-07-20 14:49:41", "level": "INFO", "message": "开始备份应用: ad-data"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144941/ad-data/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 ad-data 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: aliyun-wl"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/aliyun-wl/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 aliyun-wl 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: asda"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/asda/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 asda 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: assets"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/assets/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 assets 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: barcode"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/barcode/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 barcode 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: bigdata_new"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/bigdata_new/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 bigdata_new 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: cangchu"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/cangchu/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 cangchu 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: chery"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/chery/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 chery 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: da-data"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/da-data/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 da-data 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: ddx"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/ddx/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 ddx 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: demo"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/demo/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 demo 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: difydb_assistant"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/difydb_assistant/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 difydb_assistant 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: dxrtest"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/dxrtest/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 dxrtest 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: ecology"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/ecology/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 ecology 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: edu"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/edu/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 edu 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: haishen"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/haishen/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 haishen 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: hh"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/hh/chroma_files"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "应用 hh 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "开始备份应用: hk"}, {"timestamp": "2025-07-20 14:49:42", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144942/hk/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 hk 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: hrzl"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/hrzl/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 hrzl 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: icms"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/icms/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 icms 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: jw"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/jw/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 jw 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: ky"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/ky/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 ky 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: library collections finder"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/library collections finder/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 library collections finder 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: pop"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/pop/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 pop 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: postgres_dev"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/postgres_dev/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 postgres_dev 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: score"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/score/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 score 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: sensechat_prompt"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/sensechat_prompt/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 sensechat_prompt 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: server-mysql-app"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/server-mysql-app/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 server-mysql-app 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: Stop_lm"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/Stop_lm/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 Stop_lm 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: test"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 17 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/test/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 test 备份完成: 17 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: test2"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/test2/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 test2 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: test234"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/test234/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 test234 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: testdb"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 16 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/testdb/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 testdb 备份完成: 16 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: testjerrylu"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/testjerrylu/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 testjerrylu 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: testmysql"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/testmysql/chroma_files"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "应用 testmysql 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:43", "level": "INFO", "message": "开始备份应用: testnew"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144943/testnew/chroma_files"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "应用 testnew 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "开始备份应用: testnew2"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 documentation collection: 17 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144944/testnew2/chroma_files"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "应用 testnew2 备份完成: 17 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "开始备份应用: testnew3"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144944/testnew3/chroma_files"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "应用 testnew3 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "开始备份应用: text_DBselect"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144944/text_DBselect/chroma_files"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "应用 text_DBselect 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "开始备份应用: train_query"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144944/train_query/chroma_files"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "应用 train_query 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "开始备份应用: yb"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144944/yb/chroma_files"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "应用 yb 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "开始备份应用: yjk"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144944/yjk/chroma_files"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "应用 yjk 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "开始备份应用: ysrdnj"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144944/ysrdnj/chroma_files"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "应用 ysrdnj 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "开始备份应用: yy"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144944/yy/chroma_files"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "应用 yy 备份完成: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "开始备份应用: zhxy"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 sql collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 ddl collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "备份 documentation collection: 0 个文档"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "复制ChromaDB文件到: migration_scripts/backups/20250720_144944/zhxy/chroma_files"}, {"timestamp": "2025-07-20 14:49:44", "level": "INFO", "message": "应用 zhxy 备份完成: 0 个文档"}]}