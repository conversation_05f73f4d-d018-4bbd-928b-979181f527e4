# HelloDB 向量数据库迁移工具

## 📋 概述

这是一个用于将HelloDB向量数据库从旧系统迁移到新系统的一键迁移工具。工具支持全量迁移、单个应用迁移、迁移验证等功能。

## 🏗️ 目录结构

```
vector_migration/
├── README.md              # 使用说明文档
├── migrate.py             # 一键迁移主脚本
├── config/
│   └── migration_config.py   # 迁移配置文件
├── core/
│   ├── migrator.py        # 核心迁移工具
│   └── verifier.py        # 迁移验证工具
├── logs/                  # 日志文件目录（运行时生成）
└── temp/                  # 临时文件目录（运行时生成）
    └── reports/           # 迁移报告目录
```

## 🚀 快速开始

### 1. 环境准备

确保已安装必要的Python依赖：
```bash
pip install chromadb sqlalchemy psycopg2-binary
```

### 2. 配置检查

运行状态检查命令，确保环境配置正确：
```bash
cd /path/to/hellodb-api
python vector_migration/migrate.py status
```

### 3. 一键全量迁移

执行全量迁移所有应用：
```bash
python vector_migration/migrate.py migrate-all --verify
```

## 📖 详细使用说明

### 命令列表

#### 1. 全量迁移
```bash
# 迁移所有应用
python vector_migration/migrate.py migrate-all

# 迁移所有应用并自动验证
python vector_migration/migrate.py migrate-all --verify

# 迁移指定应用
python vector_migration/migrate.py migrate-all --apps "test,testdb,testnew2"
```

#### 2. 单个应用迁移
```bash
# 迁移单个应用
python vector_migration/migrate.py migrate-single --app test
```

#### 3. 验证迁移结果
```bash
# 验证所有应用
python vector_migration/migrate.py verify

# 验证指定应用
python vector_migration/migrate.py verify --apps "test,testdb"
```

#### 4. 系统状态检查
```bash
# 检查系统状态
python vector_migration/migrate.py status
```

### 配置说明

主要配置在 `config/migration_config.py` 中：

```python
# DashScope API配置
DASHSCOPE_API_KEY = 'your-api-key'

# 向量存储路径
OLD_VECTOR_PATH = '/data/chroma'          # 旧系统路径
NEW_VECTOR_PATH = '/data/hellodb/chroma'  # 新系统路径

# 数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'hellodb',
    'username': 'postgres',
    'password': 'password'
}
```

### 环境变量配置

可以通过环境变量覆盖配置：

```bash
export DASHSCOPE_API_KEY="your-api-key"
export DB_HOST="your-db-host"
export DB_PORT="5432"
export DB_NAME="hellodb"
export DB_USER="postgres"
export DB_PASSWORD="your-password"
```

## 🔧 生产环境部署

### 1. 线上环境运行步骤

1. **上传迁移工具**
   ```bash
   # 将vector_migration目录上传到服务器的hellodb-api目录下
   scp -r vector_migration/ user@server:/path/to/hellodb-api/
   ```

2. **设置环境变量**
   ```bash
   export DASHSCOPE_API_KEY="your-production-api-key"
   export DB_HOST="production-db-host"
   export DB_PASSWORD="production-password"
   ```

3. **检查系统状态**
   ```bash
   cd /path/to/hellodb-api
   python vector_migration/migrate.py status
   ```

4. **执行迁移**
   ```bash
   # 建议先迁移少量应用测试
   python vector_migration/migrate.py migrate-all --apps "test" --verify
   
   # 确认无误后执行全量迁移
   python vector_migration/migrate.py migrate-all --verify
   ```

### 2. 安全注意事项

- ✅ 迁移前确保有完整的数据备份
- ✅ 在非业务高峰期执行迁移
- ✅ 迁移过程中监控系统资源使用
- ✅ 保留旧系统数据直到确认新系统稳定

### 3. 回滚方案

如果迁移出现问题，可以：
1. 停止使用新系统
2. 切换回旧系统配置
3. 检查迁移日志分析问题
4. 修复问题后重新迁移

## 📊 输出文件说明

### 日志文件
- 位置: `vector_migration/logs/migration_YYYYMMDD_HHMMSS.log`
- 内容: 详细的迁移过程日志

### 迁移报告
- 位置: `vector_migration/temp/reports/migration_report_YYYYMMDD_HHMMSS.json`
- 内容: 迁移统计信息和结果详情

### 验证报告
- 位置: `vector_migration/temp/reports/verification_report_YYYYMMDD_HHMMSS.json`
- 内容: 迁移验证结果和性能测试数据

## 🐛 故障排除

### 常见问题

1. **DashScope API连接失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 验证API配额是否充足

2. **数据库连接失败**
   - 检查数据库配置信息
   - 确认数据库服务正常运行
   - 验证用户权限

3. **向量存储路径问题**
   - 确认路径存在且有读写权限
   - 检查磁盘空间是否充足

4. **迁移过程中断**
   - 查看日志文件了解具体错误
   - 可以重新运行迁移（工具支持幂等操作）

### 日志分析

查看详细日志：
```bash
tail -f vector_migration/logs/migration_*.log
```

查看错误日志：
```bash
grep "ERROR" vector_migration/logs/migration_*.log
```

## 📞 技术支持

如遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 检查系统状态和配置
3. 联系技术支持团队

---

**版本**: 1.0.0  
**更新时间**: 2025-07-21  
**维护团队**: HelloDB开发团队
