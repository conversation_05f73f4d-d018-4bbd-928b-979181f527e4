{"migration_summary": {"total_apps": 1, "success_count": 1, "error_count": 0, "results": [{"app_alias": "test", "migration_stats": {"sql": 0, "ddl": 0, "documentation": 17, "errors": []}, "verification_result": {"success": true, "details": {"sql": {"old_count": 0, "new_count": 0, "match": true}, "ddl": {"old_count": 0, "new_count": 0, "match": true}, "documentation": {"old_count": 17, "new_count": 17, "match": true}}}, "success": true}]}, "migration_log": [{"timestamp": "2025-07-20 12:45:25", "level": "INFO", "message": "开始迁移应用: test"}, {"timestamp": "2025-07-20 12:45:26", "level": "INFO", "message": "提取旧数据..."}, {"timestamp": "2025-07-20 12:45:26", "level": "INFO", "message": "从 sql collection 提取了 0 个文档"}, {"timestamp": "2025-07-20 12:45:26", "level": "INFO", "message": "从 ddl collection 提取了 0 个文档"}, {"timestamp": "2025-07-20 12:45:26", "level": "INFO", "message": "从 documentation collection 提取了 17 个文档"}, {"timestamp": "2025-07-20 12:45:26", "level": "INFO", "message": "开始数据迁移..."}, {"timestamp": "2025-07-20 12:45:30", "level": "INFO", "message": "验证迁移结果..."}, {"timestamp": "2025-07-20 12:45:30", "level": "INFO", "message": "sql 数据量匹配: 0"}, {"timestamp": "2025-07-20 12:45:30", "level": "INFO", "message": "ddl 数据量匹配: 0"}, {"timestamp": "2025-07-20 12:45:30", "level": "INFO", "message": "documentation 数据量匹配: 17"}, {"timestamp": "2025-07-20 12:45:30", "level": "SUCCESS", "message": "应用 test 迁移成功"}], "generated_at": "2025-07-20 12:45:30"}