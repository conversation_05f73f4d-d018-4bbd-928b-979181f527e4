#!/usr/bin/env python3
"""
全量迁移脚本 - 迁移所有45个应用，包括空数据的应用
"""
import os
import sys
import json
import time
from datetime import datetime

# 添加项目路径到sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from migration_scripts.migration_tool import VectorMigrationTool

def get_all_app_aliases():
    """获取所有应用的别名列表 - 直接从数据库查询"""
    try:
        # 直接使用sqlalchemy连接数据库
        from sqlalchemy import create_engine, text
        import os

        # 构建数据库连接URL
        db_host = os.getenv('DB_HOST', 'localhost')
        db_port = os.getenv('DB_PORT', '5432')
        db_name = os.getenv('DB_NAME', 'hellodb')
        db_user = os.getenv('DB_USER', 'postgres')
        db_password = os.getenv('DB_PASSWORD', 'password')

        db_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

        engine = create_engine(db_url)
        with engine.connect() as conn:
            # 先查看所有状态的应用数量
            status_result = conn.execute(text("""
                SELECT status, COUNT(*) as count
                FROM data_source_app
                GROUP BY status
                ORDER BY status
            """))
            status_counts = status_result.fetchall()
            print(f"📊 数据库中应用状态分布:")
            for status, count in status_counts:
                print(f"   {status}: {count} 个")

            # 获取活跃应用
            result = conn.execute(text("""
                SELECT DISTINCT alias
                FROM data_source_app
                WHERE status = 'active'
                ORDER BY alias
            """))
            app_aliases = [row[0] for row in result.fetchall()]
            print(f"📊 从数据库获取到 {len(app_aliases)} 个活跃应用")

            # 如果活跃应用少于预期，也获取其他状态的应用
            if len(app_aliases) < 50:  # 如果少于50个，检查其他状态
                all_result = conn.execute(text("""
                    SELECT DISTINCT alias, status
                    FROM data_source_app
                    ORDER BY alias
                """))
                all_apps = all_result.fetchall()
                print(f"📊 数据库中总共有 {len(all_apps)} 个应用")

                # 询问是否包含非活跃应用
                print("⚠️  发现活跃应用数量较少，是否包含其他状态的应用？")
                print("   输入 'all' 迁移所有应用，或按回车键仅迁移活跃应用")
                # 在自动化环境中，我们默认只迁移活跃应用
                # choice = input().strip().lower()
                # if choice == 'all':
                #     return [row[0] for row in all_apps]

            return app_aliases
    except Exception as e:
        print(f"❌ 从数据库获取应用列表失败: {str(e)}")
        print("⚠️  尝试从扫描文件获取应用列表...")

        # 备用方案：从扫描文件获取
        scan_file = 'migration_scripts/output/vector_data_scan_results.json'
        if not os.path.exists(scan_file):
            print(f"扫描结果文件也不存在: {scan_file}")
            return []

        with open(scan_file, 'r', encoding='utf-8') as f:
            scan_data = json.load(f)

        # 获取所有应用别名，包括空数据的
        all_apps = list(scan_data.get('/data/chroma', {}).keys())
        print(f"📊 从扫描文件获取到 {len(all_apps)} 个应用")
        return sorted(all_apps)

def ensure_new_vector_store_exists(app_alias, dashscope_api_key):
    """确保新的向量存储目录存在"""
    from core.hellodb.vector.chromadb_vectorsotre import ChromaDB_VectorStore
    
    new_path = f'/data/hellodb/chroma/{app_alias}'
    config = {
        'path': new_path,
        'dashscope_api_key': dashscope_api_key
    }
    
    try:
        # 创建向量存储实例，这会自动创建目录和collection
        vector_store = ChromaDB_VectorStore(config=config)
        
        # 检查collection是否存在，如果不存在则创建
        sql_count = vector_store.sql_collection.count()
        ddl_count = vector_store.ddl_collection.count()
        doc_count = vector_store.documentation_collection.count()
        
        print(f"  应用 {app_alias} 新向量存储已准备就绪")
        print(f"    SQL: {sql_count}, DDL: {ddl_count}, Documentation: {doc_count}")
        
        return True
    except Exception as e:
        print(f"  创建应用 {app_alias} 的新向量存储失败: {str(e)}")
        return False

def full_migration():
    """执行全量迁移"""
    print("🚀 开始全量迁移所有应用...")
    
    # 获取所有应用列表
    all_apps = get_all_app_aliases()
    if not all_apps:
        print("❌ 无法获取应用列表")
        return
    
    print(f"📊 发现 {len(all_apps)} 个应用需要处理")
    
    # 创建迁移工具
    dashscope_api_key = "sk-c4793b0c284e448ba1a611aa6f424062"
    tool = VectorMigrationTool(dashscope_api_key=dashscope_api_key)
    
    # 统计信息
    stats = {
        'total_apps': len(all_apps),
        'migrated_apps': 0,
        'created_apps': 0,
        'skipped_apps': 0,
        'error_apps': 0,
        'start_time': time.time(),
        'results': []
    }
    
    for i, app_alias in enumerate(all_apps, 1):
        print(f"\n📋 处理应用 {i}/{len(all_apps)}: {app_alias}")
        
        try:
            # 检查旧数据是否存在
            old_path = f'/data/chroma/{app_alias}'
            has_old_data = os.path.exists(old_path)
            
            if has_old_data:
                # 有旧数据，执行迁移
                print(f"  🔄 发现旧数据，执行迁移...")
                result = tool.migrate_single_app(app_alias)
                if result and result['success']:
                    stats['migrated_apps'] += 1
                    print(f"  ✅ 迁移成功")
                elif result and not result['success'] and result.get('message') == 'no_data':
                    # 虽然有旧目录但没有数据，创建新的空向量存储
                    print(f"  📁 旧目录存在但无数据，创建新的空向量存储...")
                    success = ensure_new_vector_store_exists(app_alias, dashscope_api_key)
                    if success:
                        stats['created_apps'] += 1
                        print(f"  ✅ 创建成功")
                    else:
                        stats['error_apps'] += 1
                        print(f"  ❌ 创建失败")

                    stats['results'].append({
                        'app_alias': app_alias,
                        'action': 'created_empty',
                        'success': success,
                        'note': 'old_dir_exists_but_no_data'
                    })
                else:
                    stats['error_apps'] += 1
                    print(f"  ❌ 迁移失败")

                    stats['results'].append({
                        'app_alias': app_alias,
                        'action': 'migrated',
                        'success': False,
                        'result': result
                    })
            else:
                # 无旧数据，创建新的空向量存储
                print(f"  📁 无旧数据，创建新的空向量存储...")
                success = ensure_new_vector_store_exists(app_alias, dashscope_api_key)
                if success:
                    stats['created_apps'] += 1
                    print(f"  ✅ 创建成功")
                else:
                    stats['error_apps'] += 1
                    print(f"  ❌ 创建失败")

                stats['results'].append({
                    'app_alias': app_alias,
                    'action': 'created',
                    'success': success
                })
            
            # 添加延迟避免过载
            time.sleep(0.5)
            
        except Exception as e:
            stats['error_apps'] += 1
            error_msg = f"处理应用 {app_alias} 时发生异常: {str(e)}"
            print(f"  ❌ {error_msg}")
            
            stats['results'].append({
                'app_alias': app_alias,
                'action': 'error',
                'success': False,
                'error': error_msg
            })
    
    # 计算总耗时
    stats['elapsed_time'] = time.time() - stats['start_time']
    
    # 保存全量迁移报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f'migration_scripts/output/full_migration_report_{timestamp}.json'
    
    report = {
        'full_migration_summary': stats,
        'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 打印总结
    print(f"\n🎉 全量迁移完成!")
    print(f"📊 总计: {stats['total_apps']} 个应用")
    print(f"✅ 迁移成功: {stats['migrated_apps']} 个")
    print(f"📁 新建成功: {stats['created_apps']} 个")
    print(f"❌ 失败: {stats['error_apps']} 个")
    print(f"⏱️  总耗时: {stats['elapsed_time']:.2f} 秒")
    print(f"📄 详细报告: {report_file}")
    
    return stats

if __name__ == "__main__":
    full_migration()
