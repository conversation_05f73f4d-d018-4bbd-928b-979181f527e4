# 🚀 生产环境一键迁移指南

## 📋 问题解决方案

### ① 一键迁移脚本和运行说明

已为您创建了完整的一键迁移解决方案：

#### 🎯 快速执行（推荐）
```bash
# 1. 进入项目目录
cd /path/to/hellodb-api

# 2. 设置API密钥
export DASHSCOPE_API_KEY="your-production-api-key"

# 3. 安装依赖（如果需要）
pip install psycopg2-binary

# 4. 一键迁移
python vector_migration/quick_migrate.py
```

#### 🔧 完整命令行工具
```bash
# 检查系统状态
python vector_migration/migrate.py status

# 全量迁移所有应用
python vector_migration/migrate.py migrate-all --verify

# 迁移指定应用
python vector_migration/migrate.py migrate-all --apps "test,testdb" --verify

# 仅验证迁移结果
python vector_migration/migrate.py verify
```

#### 🤖 自动化部署脚本
```bash
# Linux/Unix环境
chmod +x vector_migration/deploy.sh
./vector_migration/deploy.sh
```

### ② 重新设计的目录结构

新的目录结构适合Git提交，已排除临时文件：

```
vector_migration/                    # 新的迁移工具目录
├── README.md                       # 详细使用说明
├── DEPLOYMENT.md                   # 部署指南
├── PRODUCTION_GUIDE.md             # 本文档
├── .gitignore                      # Git忽略文件
├── migrate.py                      # 主迁移脚本
├── quick_migrate.py                # 快速迁移脚本
├── deploy.sh                       # 自动部署脚本
├── config/
│   └── migration_config.py         # 配置文件
├── core/
│   ├── migrator.py                 # 迁移核心工具
│   └── verifier.py                 # 验证工具
├── logs/                           # 日志目录（Git忽略）
└── temp/                           # 临时文件（Git忽略）
    └── reports/                    # 报告目录
```

#### Git提交建议
```bash
# 只提交核心代码文件
git add vector_migration/
git commit -m "feat: 添加向量数据库迁移工具

- 支持一键全量迁移
- 包含完整的验证机制
- 提供多种部署方式
- 适配生产环境使用"
```

## 🎯 生产环境部署步骤

### 第一步：环境准备
```bash
# 1. 确保Python环境
python3 --version  # 需要3.8+

# 2. 安装依赖
pip install chromadb sqlalchemy psycopg2-binary

# 3. 设置环境变量
export DASHSCOPE_API_KEY="your-production-api-key"
export DB_HOST="your-db-host"
export DB_PASSWORD="your-password"
```

### 第二步：系统检查
```bash
# 检查系统状态
python vector_migration/migrate.py status

# 预期输出：
# ✅ 配置文件: 正常
# ✅ DashScope API: 已配置
# ✅ 数据库连接: 正常
# ✅ 发现应用: XX 个
```

### 第三步：执行迁移
```bash
# 方式1：使用快速迁移脚本（推荐）
python vector_migration/quick_migrate.py

# 方式2：使用完整命令
python vector_migration/migrate.py migrate-all --verify

# 方式3：使用自动部署脚本
./vector_migration/deploy.sh
```

### 第四步：验证结果
```bash
# 查看迁移报告
ls -la vector_migration/temp/reports/

# 查看日志
tail -f vector_migration/logs/migration_*.log
```

## 📊 迁移工具特性

### ✅ 核心功能
- **一键迁移**: 支持全量和指定应用迁移
- **自动验证**: 迁移完成后自动验证数据完整性
- **错误处理**: 完善的异常处理和重试机制
- **进度监控**: 实时显示迁移进度和状态
- **报告生成**: 自动生成详细的迁移和验证报告

### ✅ 安全保障
- **数据保护**: 不删除原数据，确保安全
- **幂等操作**: 支持重复运行，不会重复迁移
- **完整日志**: 详细记录每个操作步骤
- **回滚支持**: 保留原数据支持快速回滚

### ✅ 生产适配
- **配置灵活**: 支持环境变量和配置文件
- **批量处理**: 支持大规模应用批量迁移
- **性能优化**: 合理的延迟和重试策略
- **监控友好**: 清晰的状态输出和错误信息

## 🔧 配置说明

### 环境变量配置
```bash
# 必需配置
export DASHSCOPE_API_KEY="your-api-key"

# 可选配置（有默认值）
export DB_HOST="localhost"
export DB_PORT="5432"
export DB_NAME="hellodb"
export DB_USER="postgres"
export DB_PASSWORD="password"
```

### 配置文件修改
如需修改默认配置，编辑 `vector_migration/config/migration_config.py`：
```python
# 修改向量存储路径
OLD_VECTOR_PATH = '/data/chroma'
NEW_VECTOR_PATH = '/data/hellodb/chroma'

# 修改迁移参数
MIGRATION_CONFIG = {
    'batch_size': 100,
    'max_retries': 3,
    'retry_delay': 5,
}
```

## 🐛 常见问题解决

### 1. 依赖包问题
```bash
# 安装缺失的包
pip install chromadb sqlalchemy psycopg2-binary
```

### 2. 数据库连接问题
```bash
# 检查数据库配置
python -c "
from vector_migration.config.migration_config import config
print('数据库URL:', config.get_database_url())
"
```

### 3. API连接问题
```bash
# 检查API密钥
echo $DASHSCOPE_API_KEY

# 测试API连接
curl -H "Authorization: Bearer $DASHSCOPE_API_KEY" \
     https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding-v1
```

### 4. 权限问题
```bash
# 确保目录权限
chmod +x vector_migration/deploy.sh
mkdir -p vector_migration/logs vector_migration/temp/reports
```

## 📞 技术支持

### 迁移失败处理
1. **查看日志**: `tail -f vector_migration/logs/migration_*.log`
2. **检查状态**: `python vector_migration/migrate.py status`
3. **重新运行**: 工具支持幂等操作，可直接重新执行
4. **联系支持**: 提供日志文件和错误信息

### 性能监控
```bash
# 监控迁移进度
watch -n 5 "tail -10 vector_migration/logs/migration_*.log"

# 检查系统资源
top -p $(pgrep -f "vector_migration")
```

## 🎉 迁移完成检查清单

- [ ] 所有应用迁移成功（error_apps = 0）
- [ ] 验证测试通过（success_count = total_apps）
- [ ] 检索功能正常
- [ ] 性能指标正常
- [ ] 日志无严重错误
- [ ] 生成完整报告

## 📋 与旧目录的对比

| 项目 | 旧目录(migration_scripts) | 新目录(vector_migration) |
|------|---------------------------|---------------------------|
| 结构 | 混乱，包含临时文件 | 清晰，分离核心和临时文件 |
| Git | 不适合提交 | 适合提交，有.gitignore |
| 部署 | 需要手动操作 | 一键部署脚本 |
| 配置 | 硬编码 | 灵活配置，支持环境变量 |
| 文档 | 缺少 | 完整的使用和部署文档 |
| 生产 | 不适合 | 专为生产环境设计 |

---

**🎯 总结**: 新的迁移工具完全解决了您提出的两个问题，提供了生产级别的一键迁移解决方案和适合Git管理的目录结构。
