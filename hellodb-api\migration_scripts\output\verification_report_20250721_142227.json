{"verification_summary": {"total_apps": 3, "success_count": 3, "error_count": 0, "results": [{"app_alias": "test", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 17, "total_count": 17}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 5, "response_time": 0.9069178104400635, "has_results": true}, "查询": {"result_count": 4, "response_time": 0.5650365352630615, "has_results": true}, "表结构": {"result_count": 5, "response_time": 0.5661418437957764, "has_results": true}, "用户信息": {"result_count": 4, "response_time": 0.5386002063751221, "has_results": true}}, "overall_success": true}, {"app_alias": "testdb", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 16, "total_count": 16}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 5, "response_time": 0.5102579593658447, "has_results": true}, "查询": {"result_count": 3, "response_time": 0.5242538452148438, "has_results": true}, "表结构": {"result_count": 5, "response_time": 0.5394546985626221, "has_results": true}, "用户信息": {"result_count": 3, "response_time": 0.598041296005249, "has_results": true}}, "overall_success": true}, {"app_alias": "testnew2", "data_counts": {"sql_count": 0, "ddl_count": 0, "documentation_count": 17, "total_count": 17}, "retrieval_functionality": {"documentation_retrieval": false, "sql_retrieval": false, "ddl_retrieval": false}, "embedding_quality": {"数据库": {"result_count": 5, "response_time": 0.5998349189758301, "has_results": true}, "查询": {"result_count": 4, "response_time": 0.5746722221374512, "has_results": true}, "表结构": {"result_count": 5, "response_time": 0.5861752033233643, "has_results": true}, "用户信息": {"result_count": 4, "response_time": 0.5522058010101318, "has_results": true}}, "overall_success": true}]}, "verification_log": [{"timestamp": "2025-07-21 14:22:16", "level": "INFO", "message": "开始验证 3 个迁移的应用"}, {"timestamp": "2025-07-21 14:22:16", "level": "INFO", "message": "开始验证应用: test"}, {"timestamp": "2025-07-21 14:22:16", "level": "INFO", "message": "验证应用 test 的数据数量..."}, {"timestamp": "2025-07-21 14:22:16", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-21 14:22:16", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-21 14:22:16", "level": "INFO", "message": "  Documentation collection: 17 个文档"}, {"timestamp": "2025-07-21 14:22:16", "level": "INFO", "message": "测试应用 test 的检索功能..."}, {"timestamp": "2025-07-21 14:22:17", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-21 14:22:17", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-21 14:22:18", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-21 14:22:18", "level": "INFO", "message": "测试应用 test 的embedding质量..."}, {"timestamp": "2025-07-21 14:22:18", "level": "INFO", "message": "  查询 '数据库': 5 个结果, 响应时间: 0.907秒"}, {"timestamp": "2025-07-21 14:22:19", "level": "INFO", "message": "  查询 '查询': 4 个结果, 响应时间: 0.565秒"}, {"timestamp": "2025-07-21 14:22:20", "level": "INFO", "message": "  查询 '表结构': 5 个结果, 响应时间: 0.566秒"}, {"timestamp": "2025-07-21 14:22:20", "level": "INFO", "message": "  查询 '用户信息': 4 个结果, 响应时间: 0.539秒"}, {"timestamp": "2025-07-21 14:22:20", "level": "SUCCESS", "message": "应用 test 验证成功"}, {"timestamp": "2025-07-21 14:22:20", "level": "INFO", "message": "开始验证应用: testdb"}, {"timestamp": "2025-07-21 14:22:20", "level": "INFO", "message": "验证应用 testdb 的数据数量..."}, {"timestamp": "2025-07-21 14:22:20", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-21 14:22:20", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-21 14:22:20", "level": "INFO", "message": "  Documentation collection: 16 个文档"}, {"timestamp": "2025-07-21 14:22:20", "level": "INFO", "message": "测试应用 testdb 的检索功能..."}, {"timestamp": "2025-07-21 14:22:21", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-21 14:22:21", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-21 14:22:21", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-21 14:22:21", "level": "INFO", "message": "测试应用 testdb 的embedding质量..."}, {"timestamp": "2025-07-21 14:22:22", "level": "INFO", "message": "  查询 '数据库': 5 个结果, 响应时间: 0.510秒"}, {"timestamp": "2025-07-21 14:22:22", "level": "INFO", "message": "  查询 '查询': 3 个结果, 响应时间: 0.524秒"}, {"timestamp": "2025-07-21 14:22:23", "level": "INFO", "message": "  查询 '表结构': 5 个结果, 响应时间: 0.539秒"}, {"timestamp": "2025-07-21 14:22:23", "level": "INFO", "message": "  查询 '用户信息': 3 个结果, 响应时间: 0.598秒"}, {"timestamp": "2025-07-21 14:22:23", "level": "SUCCESS", "message": "应用 testdb 验证成功"}, {"timestamp": "2025-07-21 14:22:23", "level": "INFO", "message": "开始验证应用: testnew2"}, {"timestamp": "2025-07-21 14:22:23", "level": "INFO", "message": "验证应用 testnew2 的数据数量..."}, {"timestamp": "2025-07-21 14:22:24", "level": "INFO", "message": "  SQL collection: 0 个文档"}, {"timestamp": "2025-07-21 14:22:24", "level": "INFO", "message": "  DDL collection: 0 个文档"}, {"timestamp": "2025-07-21 14:22:24", "level": "INFO", "message": "  Documentation collection: 17 个文档"}, {"timestamp": "2025-07-21 14:22:24", "level": "INFO", "message": "测试应用 testnew2 的检索功能..."}, {"timestamp": "2025-07-21 14:22:24", "level": "INFO", "message": "  Documentation检索返回空结果"}, {"timestamp": "2025-07-21 14:22:24", "level": "INFO", "message": "  SQL检索返回空结果"}, {"timestamp": "2025-07-21 14:22:25", "level": "INFO", "message": "  DDL检索返回空结果"}, {"timestamp": "2025-07-21 14:22:25", "level": "INFO", "message": "测试应用 testnew2 的embedding质量..."}, {"timestamp": "2025-07-21 14:22:25", "level": "INFO", "message": "  查询 '数据库': 5 个结果, 响应时间: 0.600秒"}, {"timestamp": "2025-07-21 14:22:26", "level": "INFO", "message": "  查询 '查询': 4 个结果, 响应时间: 0.575秒"}, {"timestamp": "2025-07-21 14:22:27", "level": "INFO", "message": "  查询 '表结构': 5 个结果, 响应时间: 0.586秒"}, {"timestamp": "2025-07-21 14:22:27", "level": "INFO", "message": "  查询 '用户信息': 4 个结果, 响应时间: 0.552秒"}, {"timestamp": "2025-07-21 14:22:27", "level": "SUCCESS", "message": "应用 testnew2 验证成功"}, {"timestamp": "2025-07-21 14:22:27", "level": "INFO", "message": "验证完成: 成功=3, 失败=0"}], "generated_at": "2025-07-21 14:22:27"}