#!/usr/bin/env python3
"""
向量数据库迁移配置文件
"""
import os
from typing import Dict, Any

class MigrationConfig:
    """迁移配置类"""
    
    def __init__(self):
        # 基础配置
        self.PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        # DashScope API配置
        self.DASHSCOPE_API_KEY = os.getenv('DASHSCOPE_API_KEY', 'sk-c4793b0c284e448ba1a611aa6f424062')
        
        # 向量存储路径配置
        self.OLD_VECTOR_PATH = '/data/chroma'
        self.NEW_VECTOR_PATH = '/data/hellodb/chroma'
        
        # 数据库配置
        self.DATABASE_CONFIG = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 5432)),
            'database': os.getenv('DB_NAME', 'hellodb'),
            'username': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', 'password')
        }
        
        # 迁移配置
        self.MIGRATION_CONFIG = {
            'batch_size': 100,  # 批处理大小
            'max_retries': 3,   # 最大重试次数
            'retry_delay': 5,   # 重试延迟(秒)
            'backup_enabled': True,  # 是否启用备份
            'verification_enabled': True,  # 是否启用验证
            'parallel_workers': 1,  # 并行工作线程数
        }
        
        # 日志配置
        self.LOG_CONFIG = {
            'level': 'INFO',
            'format': '[%(asctime)s] [%(levelname)s] %(message)s',
            'file_enabled': True,
            'console_enabled': True,
            'max_file_size': 10 * 1024 * 1024,  # 10MB
            'backup_count': 5
        }
        
        # 输出目录配置
        self.OUTPUT_DIRS = {
            'logs': os.path.join(self.PROJECT_ROOT, 'vector_migration', 'logs'),
            'temp': os.path.join(self.PROJECT_ROOT, 'vector_migration', 'temp'),
            'reports': os.path.join(self.PROJECT_ROOT, 'vector_migration', 'temp', 'reports')
        }
        
        # 确保输出目录存在
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保输出目录存在"""
        for dir_path in self.OUTPUT_DIRS.values():
            os.makedirs(dir_path, exist_ok=True)
    
    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        config = self.DATABASE_CONFIG
        return f"postgresql://{config['username']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}"
    
    def get_old_vector_path(self, app_alias: str) -> str:
        """获取旧向量存储路径"""
        return f"{self.OLD_VECTOR_PATH}/{app_alias}"
    
    def get_new_vector_path(self, app_alias: str) -> str:
        """获取新向量存储路径"""
        return f"{self.NEW_VECTOR_PATH}/{app_alias}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'dashscope_api_key': self.DASHSCOPE_API_KEY,
            'old_vector_path': self.OLD_VECTOR_PATH,
            'new_vector_path': self.NEW_VECTOR_PATH,
            'database_config': self.DATABASE_CONFIG,
            'migration_config': self.MIGRATION_CONFIG,
            'log_config': self.LOG_CONFIG,
            'output_dirs': self.OUTPUT_DIRS
        }

# 全局配置实例
config = MigrationConfig()
