{"full_migration_summary": {"total_apps": 45, "migrated_apps": 17, "created_apps": 22, "skipped_apps": 0, "error_apps": 6, "start_time": 1753087954.3874547, "results": [{"app_alias": "ad-data", "action": "migrated", "success": false, "result": {"app_alias": "ad-data", "migration_stats": {"sql": 0, "ddl": 0, "documentation": 1, "errors": ["迁移DDL数据失败: 'NoneType' object is not subscriptable"]}, "verification_result": {"success": false, "details": {"sql": {"old_count": 0, "new_count": 0, "match": true}, "ddl": {"old_count": 1, "new_count": 0, "match": false}, "documentation": {"old_count": 1, "new_count": 1, "match": true}}}, "success": false}}, {"app_alias": "ali<PERSON>-wl", "action": "migrated", "success": false, "result": {"app_alias": "ali<PERSON>-wl", "migration_stats": {"sql": 0, "ddl": 0, "documentation": 157, "errors": ["迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable"]}, "verification_result": {"success": false, "details": {"sql": {"old_count": 0, "new_count": 0, "match": true}, "ddl": {"old_count": 0, "new_count": 0, "match": true}, "documentation": {"old_count": 159, "new_count": 157, "match": false}}}, "success": false}}, {"app_alias": "asda", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "assets", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "barcode", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "bigdata_new", "action": "migrated", "success": false, "result": {"app_alias": "bigdata_new", "migration_stats": {"sql": 3, "ddl": 0, "documentation": 69, "errors": ["迁移Documentation数据失败: 'NoneType' object is not subscriptable"]}, "verification_result": {"success": false, "details": {"sql": {"old_count": 3, "new_count": 3, "match": true}, "ddl": {"old_count": 0, "new_count": 0, "match": true}, "documentation": {"old_count": 70, "new_count": 69, "match": false}}}, "success": false}}, {"app_alias": "cangchu", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "chery", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "da-data", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "ddx", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "demo", "action": "migrated", "success": false, "result": {"app_alias": "demo", "migration_stats": {"sql": 0, "ddl": 0, "documentation": 55, "errors": ["迁移Documentation数据失败: 'NoneType' object is not subscriptable"]}, "verification_result": {"success": false, "details": {"sql": {"old_count": 0, "new_count": 0, "match": true}, "ddl": {"old_count": 0, "new_count": 0, "match": true}, "documentation": {"old_count": 56, "new_count": 55, "match": false}}}, "success": false}}, {"app_alias": "dxrtest", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "ecology", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "jw", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "library collections finder", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "postgres_dev", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "sensechat_prompt", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "server-mysql-app", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "test", "action": "migrated", "success": false, "result": {"app_alias": "test", "migration_stats": {"sql": 0, "ddl": 0, "documentation": 114, "errors": ["迁移Documentation数据失败: 'NoneType' object is not subscriptable"]}, "verification_result": {"success": false, "details": {"sql": {"old_count": 0, "new_count": 0, "match": true}, "ddl": {"old_count": 0, "new_count": 0, "match": true}, "documentation": {"old_count": 115, "new_count": 114, "match": false}}}, "success": false}}, {"app_alias": "test234", "action": "created", "success": true}, {"app_alias": "testdb", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "<PERSON><PERSON><PERSON><PERSON>", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "testnew", "action": "created", "success": true}, {"app_alias": "testnew2", "action": "created", "success": true}, {"app_alias": "testnew3", "action": "created", "success": true}, {"app_alias": "ysrdnj", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "yy", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "zhxy", "action": "migrated", "success": false, "result": {"app_alias": "zhxy", "migration_stats": {"sql": 0, "ddl": 0, "documentation": 451, "errors": ["迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable", "迁移Documentation数据失败: 'NoneType' object is not subscriptable"]}, "verification_result": {"success": false, "details": {"sql": {"old_count": 0, "new_count": 0, "match": true}, "ddl": {"old_count": 0, "new_count": 0, "match": true}, "documentation": {"old_count": 477, "new_count": 451, "match": false}}}, "success": false}}], "elapsed_time": 388.73923659324646}, "generated_at": "2025-07-21 16:59:03"}