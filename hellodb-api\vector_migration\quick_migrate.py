#!/usr/bin/env python3
"""
快速迁移脚本 - 适用于生产环境的简化版本
"""
import os
import sys

def main():
    """主函数"""
    print("🚀 HelloDB向量数据库快速迁移")
    print("=" * 50)
    
    # 检查环境
    if not os.path.exists('vector_migration/migrate.py'):
        print("❌ 错误: 请在hellodb-api目录下运行此脚本")
        sys.exit(1)
    
    # 设置环境变量提示
    dashscope_key = os.getenv('DASHSCOPE_API_KEY')
    if not dashscope_key:
        print("⚠️  警告: 未设置DASHSCOPE_API_KEY环境变量")
        print("   请先设置: export DASHSCOPE_API_KEY='your-api-key'")
        
        # 询问是否继续
        response = input("是否继续？(y/N): ").strip().lower()
        if response != 'y':
            print("退出迁移")
            sys.exit(0)
    
    print("\n📋 选择迁移模式:")
    print("1. 全量迁移所有应用（推荐）")
    print("2. 迁移指定应用")
    print("3. 仅验证现有迁移")
    print("4. 检查系统状态")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == '1':
        print("\n🚀 开始全量迁移...")
        cmd = "python vector_migration/migrate.py migrate-all --verify"
    elif choice == '2':
        apps = input("请输入应用别名（逗号分隔）: ").strip()
        print(f"\n🚀 开始迁移指定应用: {apps}")
        cmd = f"python vector_migration/migrate.py migrate-all --apps '{apps}' --verify"
    elif choice == '3':
        print("\n🔍 开始验证迁移...")
        cmd = "python vector_migration/migrate.py verify"
    elif choice == '4':
        print("\n📊 检查系统状态...")
        cmd = "python vector_migration/migrate.py status"
    else:
        print("❌ 无效选择")
        sys.exit(1)
    
    print(f"执行命令: {cmd}")
    print("-" * 50)
    
    # 执行命令
    exit_code = os.system(cmd)
    
    if exit_code == 0:
        print("\n✅ 执行成功！")
        print("📁 查看结果文件:")
        print("   - 日志: vector_migration/logs/")
        print("   - 报告: vector_migration/temp/reports/")
    else:
        print("\n❌ 执行失败！")
        print("请检查日志文件了解详细错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
