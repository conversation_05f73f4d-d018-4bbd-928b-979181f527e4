"""
数据源管理控制器

该模块提供了数据源管理相关的 RESTful API 接口，包括：
- 数据源的增删改查
- 数据源连接测试
- 数据源列表获取

接口列表：
- GET    /api/datasources          获取所有数据源列表
- POST   /api/datasources          创建新的数据源
- GET    /api/datasources/<id>     获取指定数据源详情
- PUT    /api/datasources/<id>     更新指定数据源
- DELETE /api/datasources/<id>     删除指定数据源
- POST   /api/datasources/test_connection  测试数据源连接

Author: guoweiwei
Date: 2025-03-12
"""

import datetime
import logging
from flask import request
from flask_login import current_user, login_required
from flask_restful import Resource, marshal_with, reqparse
from services.chatdb_message_service import ChatdbMessageService
from services.datasource_service import DatasourceService
from fields.datasource_fields import datasource_list_fields, single_datasource_fields
from fields.chatdb_fields import chatdb_messages_response_fields
from controllers.api_response import ApiResponse
from controllers import api
from controllers.wraps import license_required
from services.errors.exception import DBTestConnectionException
from tasks.datasource_app_check_task import check_app_task

class DatasourceListApi(Resource):
    @marshal_with(datasource_list_fields)
    @login_required
    @license_required
    def get(self):
        """ 获取所有数据源列表 """
        created_by = current_user.id
        datasources = DatasourceService.get_all_datasources(created_by)
        return ApiResponse.success({
            'datasources': datasources,
            'total': len(datasources)
        })
    
    @marshal_with(single_datasource_fields)
    @login_required
    @license_required
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument('name', type=str, required=True)
        parser.add_argument('type', type=str, required=True)
        parser.add_argument('host', type=str, required=False)
        parser.add_argument('port', type=int, required=False)
        parser.add_argument('database', type=str, required=False)
        parser.add_argument('username', type=str, required=True)
        parser.add_argument('password', type=str, required=True)
        parser.add_argument('dsn', type=str, required=False)
        parser.add_argument('tds_version', type=str, required=False)
        parser.add_argument('ssl', type=bool, default=False)
        args = parser.parse_args()

        created_by = current_user.id
        args['created_by'] = created_by
        args['created_at'] = datetime.datetime.now()

        try:
            new_datasource = DatasourceService.create_datasource(args)
            return ApiResponse.success(new_datasource, "创建成功")
        except Exception as e:
            return ApiResponse.error(400, str(e))


class DatasourceApi(Resource):
    @marshal_with(single_datasource_fields)
    @login_required
    @license_required
    def get(self, datasource_id):
        datasource = DatasourceService.get_datasource_by_id(datasource_id)
        if datasource:
            return ApiResponse.success(datasource)
        return ApiResponse.error(404, "数据源未找到")
    
    
    @marshal_with(single_datasource_fields)
    @login_required
    @license_required
    def put(self, datasource_id):
        parser = reqparse.RequestParser()
        parser.add_argument('name', type=str)
        parser.add_argument('type', type=str)
        parser.add_argument('host', type=str)
        parser.add_argument('port', type=int)
        parser.add_argument('database', type=str)
        parser.add_argument('username', type=str)
        parser.add_argument('password', type=str)
        parser.add_argument('dsn', type=str)
        parser.add_argument('tds_version', type=str, required=False)
        parser.add_argument('ssl', type=bool)
        args = parser.parse_args()

        updated_by = current_user.id
        args['updated_by'] = updated_by
        args['updated_at'] = datetime.datetime.now()

        try:
            updated_datasource = DatasourceService.update_datasource(datasource_id, args)
            if updated_datasource:
                return ApiResponse.success(updated_datasource, "更新成功")
            return ApiResponse.error(404, "数据源未找到")
        except Exception as e:
            return ApiResponse.error(400, str(e))

    def delete(self, datasource_id):
        try:
            if DatasourceService.delete_datasource(datasource_id):
                return ApiResponse.success({}, "删除成功")
            return ApiResponse.error(404, "数据源未找到")
        except Exception as e:
            return ApiResponse.error(400, str(e))

class DatasourceTestConnectionApi(Resource):

    @login_required
    @license_required
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument('id', type=str)
        parser.add_argument('type', type=str)
        parser.add_argument('host', type=str)
        parser.add_argument('port', type=int)
        parser.add_argument('database', type=str)
        parser.add_argument('username', type=str)
        parser.add_argument('password', type=str)
        parser.add_argument('dsn', type=str, required=False)
        parser.add_argument('tds_version', type=str, required=False)
        parser.add_argument('ssl', type=bool, default=False)
        args = parser.parse_args()

        try:
            connection_info = {}
            if args.get('id'):
                # 如果提供了 ID，则根据 ID 获取数据源配置作为默认值
                datasource = DatasourceService.get_datasource_by_id(args['id'])
                if not datasource:
                    return ApiResponse.error(404, "数据源未找到")
                connection_info = {
                    'type': datasource.type,
                    'host': datasource.host,
                    'port': datasource.port,
                    'database': datasource.database,
                    'username': datasource.username,
                    'password': datasource.password,
                    'dsn': datasource.dsn,
                    'tds_version': datasource.tds_version,
                    'ssl': datasource.ssl
                }
            
            # 使用 HTTP 参数更新连接信息，允许覆盖默认值
            for key in ['type', 'host', 'port', 'database', 'username', 'password', 'dsn','tds_version', 'ssl']:
                if args.get(key) is not None and (not isinstance(args.get(key), str) or args.get(key).strip()):
                    connection_info[key] = args[key]

            # 检查是否所有必要的连接信息都已提供
            required_fields = {
                'oracle': ['type', 'dsn', 'username', 'password'],
                'default': ['type', 'host', 'port', 'database', 'username', 'password']
            }
            
            db_type = connection_info.get('type', '').lower()
            fields_to_check = required_fields.get(db_type, required_fields['default'])
            
            if not all(connection_info.get(field) for field in fields_to_check):
                missing_fields = [field for field in fields_to_check if not connection_info.get(field)]
                return ApiResponse.error(400, f"缺少必要的连接信息: {', '.join(missing_fields)}")

            # 格式化连接信息显示
            if db_type == 'oracle':
                connection_info['connection_string'] = connection_info.get('dsn', '')
            else:
                host = connection_info.get('host', '')
                port = connection_info.get('port', '')
                database = connection_info.get('database', '')
                connection_info['connection_string'] = f"{host}:{port}/{database}"

            result = DatasourceService.test_connection(connection_info)
            return ApiResponse.success(result, '连接成功')
        except DBTestConnectionException as e:
            return ApiResponse.error(400, f"数据库测试连接失败: {str(e)}")

# 在 api.py 中修改路由
api.add_resource(DatasourceListApi, '/api/datasources')
api.add_resource(DatasourceApi, '/api/datasources/<uuid:datasource_id>')
api.add_resource(DatasourceTestConnectionApi, '/api/datasources/test_connection')



class DataSourceAppListApi(Resource):
    
    @login_required
    @license_required
    def get(self):
        """获取应用列表"""
        created_by = current_user.id
        apps = DatasourceService.get_app_list(created_by)
        return ApiResponse.success(data=apps)
    
    @login_required
    @license_required
    def post(self):
        """创建应用"""
        data = request.get_json()
        name = data.get('name')
        alias = data.get('alias')
        description = data.get('description')
        datasource_id = data.get('datasource_id')
        initial_prompt = data.get('initial_prompt')
        
        if not name:
            return ApiResponse.error(code=400, msg='应用名称不能为空')
        if not alias:
            return ApiResponse.error(code=400, msg='应用别名不能为空')
        if not datasource_id:
            return ApiResponse.error(code=400, msg='数据源不能为空')
        
        
        # 获取当前登录用户
        created_by = current_user.id

        datasource = DatasourceService.get_datasource_by_id(datasource_id)
        if not datasource:
            return ApiResponse.error(code=404, msg='数据源不存在')
        
        try:
            app = DatasourceService.create_app(name=name, 
                                               alias=alias, 
                                               description=description,
                                               datasource_id=datasource_id, 
                                               datasource_type=datasource.type,
                                               created_by=created_by, 
                                               initial_prompt=initial_prompt)
            return ApiResponse.success(data=app.to_dict())
        except Exception as e:
            return ApiResponse.error(code=500, msg=str(e))
        

class DataSourceAppApi(Resource):

    @login_required
    @license_required
    def get(self, id):
        """获取应用详情"""
        app = DatasourceService.get_app_by_id(id)
        if app:
            return ApiResponse.success(data=app.to_dict())
        return ApiResponse.error(code=404, msg='应用不存在')
    
    @login_required
    @license_required
    def put(self, id):
        """更新应用"""
        data = request.get_json()

        if not data:
            return ApiResponse.error(code=400, msg='请求体不能为空')
        try:

            datasource_id = data.get('datasource_id')
            datasource = DatasourceService.get_datasource_by_id(datasource_id)
            if not datasource:
                return ApiResponse.error(code=404, msg='数据源不存在')

            app = DatasourceService.update_app(id=id, 
                                               name=data.get('name'), 
                                               alias=data.get('alias'),
                                               description=data.get('description'),
                                               datasource_id=data.get('datasource_id'),
                                               datasource_type=datasource.type,
                                               updated_by=current_user.id,
                                               initial_prompt=data.get('initial_prompt'))
            return ApiResponse.success(data=app.to_dict())
        except Exception as e:
           return ApiResponse.error(code=500, msg=str(e))
        

class DataSourceAppCheckApi(Resource):

    def post(self, app_id):
        """启动应用检测任务"""
        try:
            # 使用Celery异步任务
            check_app_task.delay(app_id)
            return ApiResponse.success('检测任务已启动')
        except Exception as e:
            logging.error(f'启动检测任务失败: {str(e)}')
            return ApiResponse.error(f'启动检测任务失败: {str(e)}')
        

class DataSourceAppQuery(Resource):

    @login_required
    @license_required
    def get(self):
        """ 获取数据源应用信息 """

        alias = request.args.get('alias')

        if alias:
            data_app = DatasourceService.get_app_by_alias(alias)

        if data_app:
            return ApiResponse.success(data=data_app.to_dict())
        
        return ApiResponse.error(code=404, msg='应用不存在')
    
class DataSourceAppKeyApi(Resource):
    
    @login_required
    @license_required
    def put(self, app_id):
        """ 更新数据源应用密钥 """
        app_info = DatasourceService.update_app_key(app_id)
        return ApiResponse.success(data=app_info.to_dict())


class ChatdbMessagesApi(Resource):

    @marshal_with(chatdb_messages_response_fields)
    @login_required
    @license_required
    def get(self):
        app_id = request.args.get('app_id')
        sql_valid = request.args.get('sql_valid')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        is_recommend = request.args.get('is_recommend')
        page = int(request.args.get('page', 0))
        per_page = int(request.args.get('per_page', 10))
        query_result = ChatdbMessageService.query_message(app_id, sql_valid, start_date, end_date, is_recommend=is_recommend, page=page, per_page=per_page)

        return ApiResponse.paginated_from_pagination(query_result)

class DataSourceAppStatisticsApi(Resource):
    @login_required
    @license_required
    def get(self, app_id):
        """获取应用统计信息"""
        app_statistics = DatasourceService.get_app_statistics(app_id) 
        return ApiResponse.success(data=app_statistics)

# 注册路由
api.add_resource(DataSourceAppListApi, '/api/datasource/apps')
api.add_resource(DataSourceAppApi, '/api/datasource/app/<id>')
api.add_resource(DataSourceAppCheckApi, '/api/datasource/app/<app_id>/check')
api.add_resource(DataSourceAppQuery, '/api/datasource/app/query')
api.add_resource(DataSourceAppKeyApi, '/api/datasource/app/<app_id>/app_key')
api.add_resource(DataSourceAppStatisticsApi, '/api/datasource/app/<app_id>/statistics')

api.add_resource(ChatdbMessagesApi, '/api/datasource/app/messages')