import unittest
from unittest.mock import MagicMock, patch
import pandas as pd

from core.vanna.database_interface import DatabaseInterface
from core.vanna.database_factory import DatabaseFactory
from core.vanna.mysql_database import MySQLDatabase
from core.vanna.postgresql_database import PostgreSQLDatabase
from models.datasource import Datasource


class TestDatabaseInterface(unittest.TestCase):
    
    def test_factory_creates_mysql_database(self):
        # Create a mock datasource
        datasource = Datasource()
        datasource.type = 'mysql'
        datasource.host = 'localhost'
        datasource.port = 3306
        datasource.database = 'test_db'
        datasource.username = 'test_user'
        datasource.password = 'test_password'
        
        # Create a database instance using the factory
        db = DatabaseFactory.create_database(datasource)
        
        # Check that the correct type was created
        self.assertIsInstance(db, MySQLDatabase)
    
    def test_factory_creates_postgresql_database(self):
        # Create a mock datasource
        datasource = Datasource()
        datasource.type = 'postgresql'
        datasource.host = 'localhost'
        datasource.port = 5432
        datasource.database = 'test_db'
        datasource.username = 'test_user'
        datasource.password = 'test_password'
        
        # Create a database instance using the factory
        db = DatabaseFactory.create_database(datasource)
        
        # Check that the correct type was created
        self.assertIsInstance(db, PostgreSQLDatabase)
    
    @patch('core.database.mysql_database.pymysql')
    def test_mysql_connect_to_database(self, mock_pymysql):
        # Create a mock connection
        mock_connection = MagicMock()
        mock_pymysql.connect.return_value = mock_connection
        
        # Create a mock cursor
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value = mock_cursor
        
        # Create a mock datasource
        datasource = Datasource()
        datasource.type = 'mysql'
        datasource.host = 'localhost'
        datasource.port = 3306
        datasource.database = 'test_db'
        datasource.username = 'test_user'
        datasource.password = 'test_password'
        
        # Create a database instance
        db = MySQLDatabase()
        
        # Connect to the database
        db.connect_to_database(datasource)
        
        # Check that the connection was made with the correct parameters
        mock_pymysql.connect.assert_called_once_with(
            host='localhost',
            user='test_user',
            password='test_password',
            database='test_db',
            port=3306,
            cursorclass=mock_pymysql.cursors.DictCursor
        )
        
        # Check that run_sql_is_set is True
        self.assertTrue(db.run_sql_is_set)
    
    @patch('core.database.postgresql_database.psycopg2')
    def test_postgresql_get_information_schema(self, mock_psycopg2):
        # Create a mock connection
        mock_connection = MagicMock()
        mock_psycopg2.connect.return_value = mock_connection
        
        # Create a mock cursor
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value = mock_cursor
        
        # Create a mock result set
        mock_cursor.fetchall.return_value = [
            ('public', 'users', 'id', 'integer', 'int4', 'NO', 'PRIMARY KEY', None),
            ('public', 'users', 'name', 'character varying', 'varchar', 'YES', None, None),
            ('public', 'users', 'email', 'character varying', 'varchar', 'YES', None, None)
        ]
        
        # Set up the cursor description
        mock_cursor.description = [
            ('table_schema', None, None, None, None, None, None),
            ('table_name', None, None, None, None, None, None),
            ('column_name', None, None, None, None, None, None),
            ('data_type', None, None, None, None, None, None),
            ('udt_name', None, None, None, None, None, None),
            ('is_nullable', None, None, None, None, None, None),
            ('column_default', None, None, None, None, None, None),
            ('column_comment', None, None, None, None, None, None)
        ]
        
        # Create a mock datasource
        datasource = Datasource()
        datasource.type = 'postgresql'
        datasource.host = 'localhost'
        datasource.port = 5432
        datasource.database = 'test_db'
        datasource.username = 'test_user'
        datasource.password = 'test_password'
        
        # Create a database instance
        db = PostgreSQLDatabase()
        
        # Connect to the database
        db.connect_to_database(datasource)
        
        # Mock the run_sql method to return a DataFrame
        db.run_sql = MagicMock(return_value=pd.DataFrame({
            'table_schema': ['public', 'public', 'public'],
            'table_name': ['users', 'users', 'users'],
            'column_name': ['id', 'name', 'email'],
            'data_type': ['integer', 'character varying', 'character varying'],
            'udt_name': ['int4', 'varchar', 'varchar'],
            'is_nullable': ['NO', 'YES', 'YES'],
            'column_default': ['PRIMARY KEY', None, None],
            'column_comment': [None, None, None]
        }))
        
        # Get the information schema
        schema = db.get_information_schema()
        
        # Check that the schema is a DataFrame
        self.assertIsInstance(schema, pd.DataFrame)
        
        # Check that the schema has the expected columns
        self.assertEqual(list(schema.columns), [
            'table_schema', 'table_name', 'column_name', 'data_type', 
            'udt_name', 'is_nullable', 'column_default', 'column_comment'
        ])
        
        # Check that the schema has the expected data
        self.assertEqual(schema.shape[0], 3)
        self.assertEqual(schema.iloc[0]['column_name'], 'id')
        self.assertEqual(schema.iloc[1]['column_name'], 'name')
        self.assertEqual(schema.iloc[2]['column_name'], 'email')


if __name__ == '__main__':
    unittest.main()
