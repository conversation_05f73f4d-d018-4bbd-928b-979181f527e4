import json
import chromadb
import dashscope.rerank
import dashscope.rerank.text_rerank
import pandas as pd
from chromadb.config import Settings

from core.hellodb.utils import deterministic_uuid

import dashscope
import os

class DashScopeEmbedding:
    def __init__(self, model_name="text-embedding-v1", api_key=None):
        self.model_name = model_name
        if api_key:
            dashscope.api_key = api_key

    def get_text_embedding(self, text: str) -> list:
        rsp = dashscope.TextEmbedding.call(
            model=self.model_name,
            input=text
        )
        # 兼容 dashscope 返回格式
        # rsp["output"]["embeddings"] 是 list，每个元素是 {"embedding": [...]}
        emb_list = rsp["output"]["embeddings"]
        if isinstance(emb_list[0], dict) and "embedding" in emb_list[0]:
            return emb_list[0]["embedding"]
        return emb_list[0]

class DashScopeReranker:
    def __init__(self, model_name="text-rerank-v1", api_key=None):
        self.model_name = model_name
        if api_key:
            import dashscope
            dashscope.api_key = api_key

    def flatten_text(self,text):
        if isinstance(text, list):
            return " ".join(str(t) for t in text)
        return str(text)

    def rerank(self, query: str, docs: list, top_n=5, score_threshold=0.2, **kwargs) -> list:
        # docs: [{"text": "...", "metadata": {...}}, ...]
        if not docs:
            return []
        documents = [doc["text"] for doc in docs]
        assert all(isinstance(d, str) for d in documents), f"documents must be list of str, got: {documents}"
        rsp = dashscope.rerank.text_rerank.TextReRank.call(
            model=self.model_name,
            query=query,
            documents=documents,
            top_n=top_n
        )
        if rsp["status_code"] != 200:
            raise Exception(f"Failed to rerank: {rsp['message']}")
        results = rsp["output"]["results"]
        reranked = []
        for r in results:
            idx = r["index"]
            score = r["relevance_score"]
            if score >= score_threshold:
                doc = docs[idx].copy()
                doc["score"] = score
                reranked.append(doc)
        return reranked


class ChromaDB_VectorStore:
    def __init__(self, config=None):

        if config is None:
            config = {}
        path = config.get("path")
        dashscope_api_key = config.get("dashscope_api_key") or "sk-c4793b0c284e448ba1a611aa6f424062"

        self.chroma_client = chromadb.PersistentClient(
            path=path, settings=Settings(anonymized_telemetry=False)
        )
        print(f"[ChromaDB_VectorStore] pid={os.getpid()} self_id={id(self)} chroma_client_id={id(self.chroma_client)}")
        embedding_model_name = config.get("model_name", "text-embedding-v1")
        reranker_model_name = config.get("reranker_model_name", "gte-rerank")
        self.embedding_model = DashScopeEmbedding(api_key=dashscope_api_key, model_name=embedding_model_name)
        self.reranker = DashScopeReranker(api_key=dashscope_api_key, model_name=reranker_model_name)
        self.sql_collection = self.chroma_client.get_or_create_collection(
            "sql",
            embedding_function=None
        )
        self.ddl_collection = self.chroma_client.get_or_create_collection(
            "ddl",
            embedding_function=None
        )
        self.documentation_collection = self.chroma_client.get_or_create_collection(
            "documentation",
            embedding_function=None
        )

        self.top_n = config.get("top_n", 5)
        self.similarity_top_k = config.get("similarity_top_k", 10) # 检索相似度前k个
        self.score_threshold = config.get("score_threshold", 0.2)  # 降低阈值，避免过滤掉所有结果


    def add_question_sql(self, question, sql, **kwargs):
        doc = {"question": question, "sql": sql}
        doc_str = json.dumps(doc, ensure_ascii=False)
        id = deterministic_uuid(doc_str) + "-sql"
        embeddings = self.embedding_model.get_text_embedding(doc_str)
        self.sql_collection.add(
            documents=[doc_str],
            embeddings=[embeddings],
            ids=[id]
        )
        # 新版本ChromaDB自动持久化，不需要手动调用persist()
        return id

    def add_ddl(self, ddl, **kwargs):
        id = deterministic_uuid(ddl) + "-ddl"
        embeddings = self.embedding_model.get_text_embedding(ddl)
        self.ddl_collection.add(
            documents=[ddl],
            embeddings=[embeddings],
            ids=[id]
        )
        return id
    
    def add_documentation(self, documentation, **kwargs):
        id = deterministic_uuid(documentation) + "-doc"
        embeddings = self.embedding_model.get_text_embedding(documentation)
        print("embedding type:", type(embeddings))
        if isinstance(embeddings, list):
            print("embedding len:", len(embeddings))
            print("embedding first 5:", embeddings[:5])
        elif hasattr(embeddings, 'shape'):
            print("embedding shape:", embeddings.shape)
        print("embedding example:", embeddings)
        self.documentation_collection.add(
            documents=[documentation],
            embeddings=[embeddings],
            ids=[id]
        )
        return id
    

    def query(self, collection, query_text, **kwargs):
        embedding = self.embedding_model.get_text_embedding(query_text)
        print(collection.count())
        print(collection.get())
        results = collection.query(
            query_embeddings=[embedding],
            n_results=self.similarity_top_k
        )

        docs = [
            {"text": doc, "metadata": meta}
            for doc, meta in zip(results["documents"][0], results["metadatas"][0])
        ]
        if self.reranker and docs:
            docs = self.reranker.rerank(query_text, docs, top_n=self.top_n, score_threshold=self.score_threshold)
        return docs
    

    def get_similar_question_sql(self, question, **kwargs) -> list:
        """Query similar SQL questions from the SQL collection.
        
        Args:
            question: The input question to search for similar SQL queries.
            **kwargs: Additional keyword arguments to pass to the query method.
        
        Returns:
            list: A list of similar SQL questions matching the input query.
        """
        return self.query(self.sql_collection, question, **kwargs)


    def get_related_ddl(self, question, **kwargs) -> list:
        return self.query(self.ddl_collection, question, **kwargs)


    def get_related_documentation(self, question, **kwargs) -> list:
        return self.query(self.documentation_collection, question, **kwargs)
    

    def get_training_data(self, **kwargs) -> pd.DataFrame:
        sql_data = self.sql_collection.get()

        df = pd.DataFrame()

        if sql_data is not None:
            # Extract the documents and ids
            documents = [json.loads(doc) for doc in sql_data["documents"]]
            ids = sql_data["ids"]

            # Create a DataFrame
            df_sql = pd.DataFrame(
                {
                    "id": ids,
                    "question": [doc["question"] for doc in documents],
                    "content": [doc["sql"] for doc in documents],
                }
            )

            df_sql["training_data_type"] = "sql"

            df = pd.concat([df, df_sql])

        ddl_data = self.ddl_collection.get()

        if ddl_data is not None:
            # Extract the documents and ids
            documents = [doc for doc in ddl_data["documents"]]
            ids = ddl_data["ids"]

            # Create a DataFrame
            df_ddl = pd.DataFrame(
                {
                    "id": ids,
                    "question": [None for doc in documents],
                    "content": [doc for doc in documents],
                }
            )

            df_ddl["training_data_type"] = "ddl"

            df = pd.concat([df, df_ddl])

        doc_data = self.documentation_collection.get()

        if doc_data is not None:
            # Extract the documents and ids
            documents = [doc for doc in doc_data["documents"]]
            ids = doc_data["ids"]

            # Create a DataFrame
            df_doc = pd.DataFrame(
                {
                    "id": ids,
                    "question": [None for doc in documents],
                    "content": [doc for doc in documents],
                }
            )

            df_doc["training_data_type"] = "documentation"

            df = pd.concat([df, df_doc])

        return df

    def remove_training_data(self, id: str, **kwargs) -> bool:
        if id.endswith("-sql"):
            self.sql_collection.delete(ids=id)
            return True
        elif id.endswith("-ddl"):
            self.ddl_collection.delete(ids=id)
            return True
        elif id.endswith("-doc"):
            self.documentation_collection.delete(ids=id)
            return True
        else:
            return False

    def remove_collection(self, collection_name: str) -> bool:
        """
        This function can reset the collection to empty state.

        Args:
            collection_name (str): sql or ddl or documentation

        Returns:
            bool: True if collection is deleted, False otherwise
        """
        if collection_name == "sql":
            self.chroma_client.delete_collection(name="sql")
            self.sql_collection = self.chroma_client.get_or_create_collection(
                name="sql",
                embedding_function=None
            )
            return True
        elif collection_name == "ddl":
            self.chroma_client.delete_collection(name="ddl")
            self.ddl_collection = self.chroma_client.get_or_create_collection(
                name="ddl",
                embedding_function=None
            )
            return True
        elif collection_name == "documentation":
            self.chroma_client.delete_collection(name="documentation")
            self.documentation_collection = self.chroma_client.get_or_create_collection(
                name="documentation",
                embedding_function=None
            )
            return True
        else:
            return False
