{"full_migration_summary": {"total_apps": 45, "migrated_apps": 3, "created_apps": 42, "skipped_apps": 0, "error_apps": 0, "start_time": 1753060765.6389296, "results": [{"app_alias": "Stop_lm", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "ad-data", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "ali<PERSON>-wl", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "asda", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "assets", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "barcode", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "bigdata_new", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "cangchu", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "chery", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "da-data", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "ddx", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "demo", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "difydb_assistant", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "dxrtest", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "ecology", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "edu", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "haishen", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "hh", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "hk", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "hrzl", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "icms", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "jw", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "ky", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "library collections finder", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "pop", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "postgres_dev", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "score", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "sensechat_prompt", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "server-mysql-app", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "test2", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "test234", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "<PERSON><PERSON><PERSON><PERSON>", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "testmysql", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "testnew", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "testnew3", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "text_DBselect", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "train_query", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "yb", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "yjk", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "ysrdnj", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "yy", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}, {"app_alias": "zhxy", "action": "created_empty", "success": true, "note": "old_dir_exists_but_no_data"}], "elapsed_time": 42.652830839157104}, "generated_at": "2025-07-21 09:20:08"}