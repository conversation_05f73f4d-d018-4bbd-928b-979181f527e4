#!/usr/bin/env python3
"""
一键迁移脚本 - 基于现有migration_scripts工具
"""
import os
import sys
import argparse
from datetime import datetime

def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    HelloDB 向量数据库一键迁移                    ║
║                                                              ║
║  基于现有migration_scripts工具的生产环境部署版本                 ║
║  版本: 1.0.0                                                 ║
║  时间: 2025-07-21                                            ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_environment():
    """检查环境"""
    print("📋 环境检查...")
    
    # 检查当前目录
    if not os.path.exists('migration_scripts/migration_tool.py'):
        print("❌ 错误: 请在hellodb-api目录下运行此脚本")
        return False
    
    # 检查Python依赖
    try:
        import chromadb
        import sqlalchemy
        print("✅ 核心依赖: chromadb, sqlalchemy 已安装")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install chromadb sqlalchemy psycopg2-binary")
        return False
    
    # 检查API密钥
    dashscope_key = os.getenv('DASHSCOPE_API_KEY')
    if not dashscope_key:
        print("⚠️  警告: 未设置DASHSCOPE_API_KEY环境变量")
        print("   建议设置: export DASHSCOPE_API_KEY='your-api-key'")
        
        response = input("是否继续？(y/N): ").strip().lower()
        if response != 'y':
            return False
    else:
        print("✅ DashScope API密钥: 已设置")
    
    return True

def run_migration(mode, apps=None):
    """运行迁移"""
    print(f"\n🚀 开始执行迁移 - 模式: {mode}")
    print("=" * 50)
    
    if mode == "full":
        print("执行全量迁移...")
        cmd = "python migration_scripts/full_migration.py"
    elif mode == "specified" and apps:
        print(f"迁移指定应用: {apps}")
        # 这里可以扩展migration_tool.py来支持指定应用
        cmd = f"python migration_scripts/migration_tool.py --apps {apps}"
    elif mode == "verify":
        print("验证迁移结果...")
        cmd = "python migration_scripts/verify_migration.py"
    else:
        print("❌ 无效的迁移模式")
        return False
    
    print(f"执行命令: {cmd}")
    print("-" * 50)
    
    # 执行命令
    exit_code = os.system(cmd)
    
    if exit_code == 0:
        print("\n✅ 执行成功！")
        return True
    else:
        print("\n❌ 执行失败！")
        return False

def show_results():
    """显示结果文件"""
    print("\n📁 查看结果文件:")
    
    # 显示最新的报告文件
    output_dir = "migration_scripts/output"
    if os.path.exists(output_dir):
        files = os.listdir(output_dir)
        
        # 迁移报告
        migration_reports = [f for f in files if f.startswith('migration_report_') or f.startswith('full_migration_report_')]
        if migration_reports:
            latest_migration = sorted(migration_reports)[-1]
            print(f"   📊 最新迁移报告: {output_dir}/{latest_migration}")
        
        # 验证报告
        verification_reports = [f for f in files if f.startswith('verification_report_')]
        if verification_reports:
            latest_verification = sorted(verification_reports)[-1]
            print(f"   🔍 最新验证报告: {output_dir}/{latest_verification}")
        
        # 总结报告
        summary_files = [f for f in files if f.endswith('_summary.md')]
        if summary_files:
            latest_summary = sorted(summary_files)[-1]
            print(f"   📋 迁移总结: {output_dir}/{latest_summary}")
    
    print(f"   📝 详细日志请查看控制台输出")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='HelloDB向量数据库一键迁移工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 全量迁移（推荐）
  python migration_scripts/one_click_migrate.py --mode full
  
  # 迁移指定应用
  python migration_scripts/one_click_migrate.py --mode specified --apps "test,testdb"
  
  # 仅验证迁移结果
  python migration_scripts/one_click_migrate.py --mode verify
  
  # 交互式选择
  python migration_scripts/one_click_migrate.py
        """
    )
    
    parser.add_argument('--mode', choices=['full', 'specified', 'verify'], 
                       help='迁移模式: full(全量), specified(指定应用), verify(仅验证)')
    parser.add_argument('--apps', type=str, 
                       help='指定要迁移的应用列表，逗号分隔（仅在specified模式下使用）')
    
    args = parser.parse_args()
    
    # 打印横幅
    print_banner()
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请解决问题后重试")
        sys.exit(1)
    
    # 确定迁移模式
    if args.mode:
        mode = args.mode
        apps = args.apps
    else:
        # 交互式选择
        print("\n📋 请选择迁移模式:")
        print("1. 全量迁移所有应用（推荐）")
        print("2. 迁移指定应用")
        print("3. 仅验证现有迁移")
        print("4. 退出")
        
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == '1':
            mode = 'full'
            apps = None
        elif choice == '2':
            apps = input("请输入应用别名（逗号分隔）: ").strip()
            mode = 'specified'
        elif choice == '3':
            mode = 'verify'
            apps = None
        elif choice == '4':
            print("退出迁移")
            sys.exit(0)
        else:
            print("❌ 无效选择")
            sys.exit(1)
    
    # 执行迁移
    success = run_migration(mode, apps)
    
    # 显示结果
    show_results()
    
    # 打印最终结果
    print(f"\n🎯 迁移结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("🎉 迁移任务完成！")
        print("📋 建议:")
        print("   1. 查看上述报告文件了解详细信息")
        print("   2. 进行用户验收测试")
        print("   3. 监控新系统运行状态")
    else:
        print("⚠️  迁移过程中遇到问题")
        print("📋 建议:")
        print("   1. 查看错误日志分析问题")
        print("   2. 检查环境配置")
        print("   3. 联系技术支持")

if __name__ == "__main__":
    main()
